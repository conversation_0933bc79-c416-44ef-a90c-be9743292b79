import { getServerSession } from 'next-auth/next';
import { redirect } from '@/i18n/routing';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import Order from '@/models/Order';
import { CheckCircle, AlertCircle, ArrowLeft, Calendar, Clock, CreditCard } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import { Locale } from '@/i18n/config';
import { LAUNCH_OPTIONS, formatPrice, PRICING_CONFIG } from '@/constants/pricing';
import mongoose from 'mongoose';
import LaunchDateClient from './LaunchDateClient';

interface PageProps {
  params: Promise<{
    locale: Locale;
    toolId: string;
  }>;
  searchParams: Promise<{
    mode?: 'edit' | 'create';
  }>;
}

// 获取最早可选择的免费日期（一个月后）
function getMinFreeDate() {
  const date = new Date();
  date.setMonth(date.getMonth() + 1);
  return date.toISOString().split('T')[0];
}

// 获取最早可选择的付费日期（明天）
function getMinPaidDate() {
  const date = new Date();
  date.setDate(date.getDate() + 1);
  return date.toISOString().split('T')[0];
}

async function getToolData(toolId: string, userEmail: string, isEditMode: boolean = false) {
  try {
    await dbConnect();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(toolId)) {
      return null;
    }

    // 查找工具
    const tool = await Tool.findById(toolId).lean();
    if (!tool) {
      return null;
    }

    // 查找用户
    const user = await User.findOne({ email: userEmail }).lean();
    if (!user) {
      return null;
    }

    // 检查工具是否属于当前用户
    if (tool.submittedBy.toString() !== user._id.toString()) {
      return null;
    }

    // 根据模式检查工具状态
    if (isEditMode) {
      // 编辑模式：工具必须已经设置了发布日期，且状态不能是已发布
      if (!tool.selectedLaunchDate || tool.status === 'published') {
        return null;
      }
    } else {
      // 创建模式：工具状态必须是 draft
      if (tool.status !== 'draft') {
        return null;
      }
    }

    // 查找相关的订单信息
    let hasPaidOrder = false;
    let orderId = null;

    if (tool.launchOption === 'paid') {
      const order = await Order.findOne({
        toolId: tool._id,
        status: 'completed'
      }).lean();

      if (order) {
        hasPaidOrder = true;
        orderId = order._id.toString();
      }
    }

    return {
      _id: tool._id.toString(),
      name: tool.name,
      description: tool.description,
      status: tool.status,
      launchOption: tool.launchOption,
      selectedLaunchDate: tool.selectedLaunchDate,
      hasPaidOrder,
      orderId
    };
  } catch (error) {
    console.error('Failed to fetch tool:', error);
    return null;
  }
}

export default async function LaunchDatePage({ params, searchParams }: PageProps) {
  const { locale, toolId } = await params;
  const { mode = 'create' } = await searchParams;

  // 获取翻译
  const t = await getTranslations('launch');
  const tCommon = await getTranslations('common');

  // 检查用户认证
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    redirect('/');
  }

  // 根据模式获取工具数据
  const isEditMode = mode === 'edit';
  const toolData = await getToolData(toolId, session.user.email, isEditMode);

  if (!toolData) {
    redirect('/');
  }

  // 计算日期相关数据
  const minFreeDate = getMinFreeDate();
  const minPaidDate = getMinPaidDate();
  const currentDate = toolData.selectedLaunchDate ?
    new Date(toolData.selectedLaunchDate).toISOString().split('T')[0] :
    (toolData.hasPaidOrder ? minPaidDate : minFreeDate);

  // 获取发布选项
  const launchOptions = LAUNCH_OPTIONS;

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        {isEditMode && (
          <a
            href="/profile/submitted"
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            {tCommon('back')}
          </a>
        )}

        {isEditMode ? (
          <>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {t('edit_launch_date')}
            </h1>
            <p className="text-gray-600">
              {t('edit_launch_date_description')}
            </p>
          </>
        ) : (
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {t('tool_submitted_success')}
            </h1>
            <p className="text-lg text-gray-600">
              {t('select_launch_date_prompt')}
            </p>
          </div>
        )}
      </div>

      {/* Tool Info */}
      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {toolData.name}
        </h2>
        <p className="text-gray-600">{toolData.description}</p>
      </div>

      {/* Status Info for Edit Mode */}
      {isEditMode && (
        <div className="mb-8">
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-2 ${
                toolData.status === 'pending' ? 'bg-yellow-400' :
                toolData.status === 'approved' ? 'bg-green-400' :
                'bg-gray-400'
              }`} />
              <span className="text-gray-600">
                {t(`status.${toolData.status}`)}
              </span>
            </div>

            {toolData.hasPaidOrder && (
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600 font-medium">
                  {t('paid_plan')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Server-side rendered Launch Date Selector */}
      <div className="space-y-8">
        {/* 选项选择 - 如果已付费则不显示 */}
        {!toolData.hasPaidOrder && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('select_plan')}
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              {launchOptions.map((option) => (
                <div
                  key={option.id}
                  className={`relative border-2 rounded-lg p-6 transition-all border-gray-200 hover:border-gray-300 ${
                    'recommended' in option && option.recommended ? 'ring-2 ring-blue-200' : ''
                  }`}
                >
                  {'recommended' in option && option.recommended && (
                    <div className="absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      {t('recommended')}
                    </div>
                  )}

                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">
                        {t(`plans.${option.id}.title`)}
                      </h4>
                      <p className="text-2xl font-bold text-gray-900 mt-1">
                        {option.id === 'free' ? t('free') : formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale)}
                      </p>
                    </div>
                    <div className="text-right">
                      {option.id === 'free' ? (
                        <Clock className="h-6 w-6 text-gray-400" />
                      ) : (
                        <CreditCard className="h-6 w-6 text-blue-500" />
                      )}
                    </div>
                  </div>

                  <p className="text-gray-600 text-sm mb-4">
                    {t(`plans.${option.id}.description`)}
                  </p>

                  <ul className="space-y-2">
                    {option.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {t(`plans.${option.id}.features.${index}`)}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 日期选择器 */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            <Calendar className="h-5 w-5 inline mr-2" />
            {t('select_launch_date')}
          </h3>

          <LaunchDateClient
            toolId={toolId}
            locale={locale}
            currentOption={toolData.hasPaidOrder ? 'paid' : toolData.launchOption}
            currentDate={currentDate}
            minFreeDate={minFreeDate}
            minPaidDate={minPaidDate}
            hasPaidOrder={toolData.hasPaidOrder}
            orderId={toolData.orderId}
            isEditMode={isEditMode}
          />
        </div>
      </div>
    </div>
  );
}
