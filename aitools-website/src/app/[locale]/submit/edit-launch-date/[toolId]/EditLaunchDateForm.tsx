'use client';

import { useState } from 'react';
import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { Calendar, CreditCard, CheckCircle } from 'lucide-react';
import { formatPrice, PRICING_CONFIG } from '@/constants/pricing';

interface EditLaunchDateFormProps {
  toolId: string;
  locale: string;
  currentOption: 'free' | 'paid';
  currentDate: string;
  minFreeDate: string;
  minPaidDate: string;
  hasPaidOrder: boolean;
  orderId?: string;
}

export default function EditLaunchDateForm({
  toolId,
  locale,
  currentOption,
  currentDate,
  minFreeDate,
  minPaidDate,
  hasPaidOrder,
  orderId
}: EditLaunchDateFormProps) {
  const router = useRouter();
  const t = useTranslations('launch');
  
  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>(
    hasPaidOrder ? 'paid' : currentOption
  );
  const [selectedDate, setSelectedDate] = useState<string>(currentDate);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleOptionChange = (option: 'free' | 'paid') => {
    // 如果已经付费，不允许切换选项
    if (hasPaidOrder) {
      return;
    }

    setSelectedOption(option);
    // 当切换选项时，重新设置日期
    if (option === 'free') {
      setSelectedDate(minFreeDate);
    } else {
      setSelectedDate(minPaidDate);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedDate) {
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      // 如果已经有付费订单，只允许修改日期，不允许改变付费状态
      if (hasPaidOrder) {
        // 使用PATCH方法更新日期，保持付费状态
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-Locale': locale,
          },
          body: JSON.stringify({
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          router.push(`/submit/success?toolId=${toolId}&paid=true`);
        } else {
          setError(data.message || t('update_failed'));
        }
      } else if (selectedOption === 'paid' && currentOption === 'free') {
        // 如果选择了付费选项且当前是免费用户，需要创建新的订单
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Locale': locale,
          },
          body: JSON.stringify({
            launchOption: 'paid',
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          // 跳转到支付页面
          router.push(data.data.paymentUrl);
        } else {
          setError(data.message || t('create_order_failed'));
        }
      } else {
        // 使用PATCH方法更新日期
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-Locale': locale,
          },
          body: JSON.stringify({
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          router.push(`/submit/success?toolId=${toolId}&paid=${selectedOption === 'paid'}`);
        } else {
          setError(data.message || t('update_failed'));
        }
      }
    } catch {
      setError(t('network_error'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 选项选择 - 只有未付费用户才显示 */}
      {!hasPaidOrder && (
        <div className="grid md:grid-cols-2 gap-4">
          <div
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              selectedOption === 'free'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleOptionChange('free')}
          >
            <div className="flex items-center">
              <input
                type="radio"
                name="launchOption"
                value="free"
                checked={selectedOption === 'free'}
                onChange={() => handleOptionChange('free')}
                className="sr-only"
              />
              <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                selectedOption === 'free'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              }`}>
                {selectedOption === 'free' && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <div>
                <div className="font-medium text-gray-900">{t('free_option')}</div>
                <div className="text-sm text-gray-600">{t('free_description')}</div>
              </div>
            </div>
          </div>

          <div
            className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
              selectedOption === 'paid'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleOptionChange('paid')}
          >
            <div className="flex items-center">
              <input
                type="radio"
                name="launchOption"
                value="paid"
                checked={selectedOption === 'paid'}
                onChange={() => handleOptionChange('paid')}
                className="sr-only"
              />
              <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                selectedOption === 'paid'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              }`}>
                {selectedOption === 'paid' && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <div>
                <div className="font-medium text-gray-900">{t('paid_option')}</div>
                <div className="text-sm text-gray-600">{t('paid_description')}</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 日期选择 */}
      <div>
        <label htmlFor="launchDate" className="block text-sm font-medium text-gray-700 mb-2">
          {t('launch_date')}
        </label>
        <input
          type="date"
          id="launchDate"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          min={hasPaidOrder || selectedOption === 'paid' ? minPaidDate : minFreeDate}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        />
        <p className="text-sm text-gray-500 mt-1">
          {hasPaidOrder || selectedOption === 'paid' 
            ? t('paid_date_hint') 
            : t('free_date_hint')
          }
        </p>
      </div>

      {/* 提交按钮 */}
      <button
        type="submit"
        disabled={!selectedDate || isSubmitting}
        className="w-full bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        {isSubmitting ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {selectedOption === 'paid' ? t('processing') : t('saving')}
          </>
        ) : (
          <>
            {hasPaidOrder ? (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('save_changes')}
              </>
            ) : selectedOption === 'paid' ? (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                {t('upgrade_and_pay', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice) })}
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                {t('save_changes')}
              </>
            )}
          </>
        )}
      </button>
      
      {error && (
        <p className="text-red-600 text-sm mt-4">{error}</p>
      )}
      
      <p className="text-gray-500 text-sm mt-4">
        {hasPaidOrder
          ? t('changes_effective')
          : selectedOption === 'paid'
          ? t('payment_redirect')
          : t('changes_effective')
        }
      </p>
    </form>
  );
}
