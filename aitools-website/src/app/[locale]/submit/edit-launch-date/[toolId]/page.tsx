import { redirect } from 'next/navigation';
import { Locale } from '@/i18n/config';

interface PageProps {
  params: Promise<{ locale: Locale; toolId: string }>;
}

// 服务端日期计算函数
function getMinFreeDate(): string {
  const date = new Date();
  date.setMonth(date.getMonth() + 1);
  return date.toISOString().split('T')[0];
}

function getMinPaidDate(): string {
  const date = new Date();
  date.setDate(date.getDate() + 1);
  return date.toISOString().split('T')[0];
}

function formatDateForLocale(dateString: string, locale: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US');
}

async function getToolData(toolId: string, userEmail: string) {
  try {
    await dbConnect();

    if (!mongoose.Types.ObjectId.isValid(toolId)) {
      return null;
    }

    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return null;
    }

    const tool = await Tool.findById(toolId);
    if (!tool) {
      return null;
    }

    // 检查工具所有权
    if (!tool.submittedBy || tool.submittedBy.toString() !== user._id.toString()) {
      return null;
    }

    // 检查状态是否允许修改
    if (!['pending', 'approved'].includes(tool.status)) {
      return { error: '当前状态不允许修改发布日期' };
    }

    // 检查是否已经发布
    const now = new Date();
    if (tool.selectedLaunchDate && new Date(tool.selectedLaunchDate) <= now && tool.status === 'approved') {
      return { error: '工具已发布，无法修改发布日期' };
    }

    // 检查是否有成功支付的订单
    let hasPaidOrder = false;
    let paidOrder = null;
    if (tool.orderId) {
      paidOrder = await Order.findById(tool.orderId);
      hasPaidOrder = paidOrder && paidOrder.status === 'completed';
    }

    // 如果没有关联订单但工具标记为付费，查找相关的已支付订单
    if (!hasPaidOrder && tool.launchOption === 'paid') {
      paidOrder = await Order.findOne({
        toolId: tool._id,
        userId: user._id,
        type: 'launch_date_priority',
        status: 'completed'
      }).sort({ createdAt: -1 });
      hasPaidOrder = !!paidOrder;
    }

    return {
      _id: tool._id.toString(),
      name: tool.name,
      description: tool.description,
      status: tool.status,
      launchOption: tool.launchOption,
      selectedLaunchDate: tool.selectedLaunchDate ? tool.selectedLaunchDate.toISOString() : null,
      hasPaidOrder,
      orderId: paidOrder?._id?.toString() || tool.orderId,
    };
  } catch (error) {
    console.error('Failed to fetch tool:', error);
    return null;
  }
}

export default async function EditLaunchDatePage({ params }: PageProps) {
  const session = await getServerSession(authOptions);
  const { locale, toolId } = await params;

  // 获取翻译
  const t = await getTranslations('launch');
  const tCommon = await getTranslations('common');

  // 检查用户是否已登录
  if (!session?.user?.email) {
    redirect('/');
  }

  // 获取工具数据
  const toolData = await getToolData(toolId, session.user.email);

  if (!toolData) {
    redirect('/');
  }

  // 如果有错误，显示错误页面
  if ('error' in toolData) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{tCommon('error')}</h1>
          <p className="text-gray-600 mb-4">{toolData.error}</p>
          <a
            href="/profile/submitted"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 inline-block"
          >
            {tCommon('back_to_tools')}
          </a>
        </div>
      </div>
    );
  }

  // 计算日期相关数据
  const minFreeDate = getMinFreeDate();
  const minPaidDate = getMinPaidDate();
  const currentDate = toolData.selectedLaunchDate ?
    new Date(toolData.selectedLaunchDate).toISOString().split('T')[0] :
    (toolData.hasPaidOrder ? minPaidDate : minFreeDate);

  // 获取发布选项
  const launchOptions = LAUNCH_OPTIONS;

  return (
    <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <a
          href="/profile/submitted"
          className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          {tCommon('back')}
        </a>

        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('edit_launch_date')}
        </h1>
        <p className="text-gray-600">
          {t('edit_launch_date_description')}
        </p>
      </div>

      {/* Tool Info */}
      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {toolData.name}
        </h2>
        <p className="text-gray-600 mb-4">{toolData.description}</p>

        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm text-gray-600">
            <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            {t('current_plan')}: {toolData.hasPaidOrder ? t('priority_service_paid') : toolData.launchOption === 'paid' ? t('priority_service') : t('free_service')}
          </div>

          {toolData.selectedLaunchDate && (
            <div className="text-sm text-gray-600">
              {t('current_launch_date')}: <span className="font-medium">
                {formatDateForLocale(toolData.selectedLaunchDate, locale)}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Server-side rendered Launch Date Selector */}
      <div className="space-y-8">
        {/* 选项选择 - 如果已付费则不显示 */}
        {!toolData.hasPaidOrder && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('select_plan')}
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              {launchOptions.map((option) => (
                <div
                  key={option.id}
                  className={`relative border-2 rounded-lg p-6 transition-all border-gray-200 hover:border-gray-300 ${
                    'recommended' in option && option.recommended ? 'ring-2 ring-blue-200' : ''
                  }`}
                >
                  {'recommended' in option && option.recommended && (
                    <div className="absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                      {t('recommended')}
                    </div>
                  )}

                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="mr-3">
                        {option.id === 'free' ? (
                          <Clock className="h-6 w-6 text-blue-500" />
                        ) : (
                          <CreditCard className="h-6 w-6 text-green-500" />
                        )}
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">{option.name}</h4>
                        <p className="text-sm text-gray-600">{option.description}</p>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {formatPrice(option.price)}
                      </div>
                    </div>
                  </div>

                  <ul className="space-y-2">
                    {option.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 已付费用户的提示信息 */}
        {toolData.hasPaidOrder && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              <div>
                <h4 className="font-medium text-green-800">{t('priority_service_activated')}</h4>
                <p className="text-sm text-green-600 mt-1">{t('priority_service_description')}</p>
              </div>
            </div>
          </div>
        )}

        {/* 日期选择器 */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            <Calendar className="h-5 w-5 inline mr-2" />
            {t('select_launch_date')}
          </h3>

          <EditLaunchDateForm
            toolId={toolId}
            locale={locale}
            currentOption={toolData.hasPaidOrder ? 'paid' : toolData.launchOption}
            currentDate={currentDate}
            minFreeDate={minFreeDate}
            minPaidDate={minPaidDate}
            hasPaidOrder={toolData.hasPaidOrder}
            orderId={toolData.orderId}
          />
        </div>
      </div>
    </div>
  );
}
