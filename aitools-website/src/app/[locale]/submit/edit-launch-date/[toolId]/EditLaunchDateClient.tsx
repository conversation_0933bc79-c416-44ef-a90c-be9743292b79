'use client';

import { useState } from 'react';
import { useRouter } from '@/i18n/routing';
import LaunchDateSelector from '@/components/LaunchDateSelector';

interface EditLaunchDateClientProps {
  toolId: string;
  locale: string;
  currentOption?: 'free' | 'paid';
  currentDate?: string;
  hasPaidOrder?: boolean;
  orderId?: string;
}

export default function EditLaunchDateClient({
  toolId,
  locale,
  currentOption = 'free',
  currentDate,
  hasPaidOrder = false,
  orderId
}: EditLaunchDateClientProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (option: 'free' | 'paid', selectedDate: string) => {
    setIsSubmitting(true);
    setError('');

    try {
      // 如果已经有付费订单，只允许修改日期，不允许改变付费状态
      if (hasPaidOrder) {
        // 使用PATCH方法更新日期，保持付费状态
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-Locale': locale,
          },
          body: JSON.stringify({
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          router.push(`/submit/success?toolId=${toolId}&paid=true`);
        } else {
          setError(data.message || '修改失败');
        }
      } else if (option === 'paid' && currentOption === 'free') {
        // 如果选择了付费选项且当前是免费用户，需要创建新的订单
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Locale': locale,
          },
          body: JSON.stringify({
            launchOption: 'paid',
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          // 跳转到支付页面
          router.push(data.data.paymentUrl);
        } else {
          setError(data.message || '创建订单失败');
        }
      } else {
        // 使用PATCH方法更新日期
        const response = await fetch(`/api/tools/${toolId}/launch-date`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'X-Locale': locale,
          },
          body: JSON.stringify({
            selectedDate,
          }),
        });

        const data = await response.json();

        if (data.success) {
          router.push(`/submit/success?toolId=${toolId}&paid=${option === 'paid'}`);
        } else {
          setError(data.message || '修改失败');
        }
      }
    } catch {
      setError('网络错误，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <LaunchDateSelector
      toolId={toolId}
      currentOption={hasPaidOrder ? 'paid' : currentOption}
      currentDate={currentDate}
      isEditing={true}
      onSubmit={handleSubmit}
      isSubmitting={isSubmitting}
      error={error}
      hasPaidOrder={hasPaidOrder}
    />
  );
}
