{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/submit/success/EditLaunchDateButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\n\ninterface EditLaunchDateButtonProps {\n  toolId: string;\n  toolStatus: string;\n}\n\nexport default function EditLaunchDateButton({ toolId, toolStatus }: EditLaunchDateButtonProps) {\n  const router = useRouter();\n  const t = useTranslations('submit.success');\n\n  const handleEditLaunchDate = () => {\n    // 跳转到发布日期选择页面\n    router.push(`/submit/launch-date?toolId=${toolId}`);\n  };\n\n  // 只有在 pending 或 approved 状态下才显示按钮\n  if (!['pending', 'approved'].includes(toolStatus)) {\n    return null;\n  }\n\n  return (\n    <button\n      onClick={handleEditLaunchDate}\n      className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\"\n    >\n      {t('edit_launch_date')}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,qBAAqB,EAAE,MAAM,EAAE,UAAU,EAA6B;;IAC5F,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,uBAAuB;QAC3B,cAAc;QACd,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,QAAQ;IACpD;IAEA,kCAAkC;IAClC,IAAI,CAAC;QAAC;QAAW;KAAW,CAAC,QAAQ,CAAC,aAAa;QACjD,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;kBAET,EAAE;;;;;;AAGT;GAtBwB;;QACP,yHAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;;;KAFH", "debugId": null}}]}