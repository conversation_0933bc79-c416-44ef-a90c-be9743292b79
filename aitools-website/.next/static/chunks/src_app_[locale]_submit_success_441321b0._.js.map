{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/submit/success/ActionButtons.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\n\ninterface ActionButtonsProps {\n  locale: string;\n}\n\nexport default function ActionButtons({ locale }: ActionButtonsProps) {\n  const router = useRouter();\n  const t = useTranslations('submit.success');\n\n  const handleViewSubmissions = () => {\n    router.push('/profile/submitted');\n  };\n\n  const handleBackToHome = () => {\n    router.push('/');\n  };\n\n  return (\n    <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n      <button\n        onClick={handleViewSubmissions}\n        className=\"bg-blue-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors\"\n      >\n        {t('view_submissions')}\n      </button>\n      <button\n        onClick={handleBackToHome}\n        className=\"bg-gray-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-gray-700 transition-colors\"\n      >\n        {t('back_to_home')}\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,cAAc,EAAE,MAAM,EAAsB;;IAClE,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,wBAAwB;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET,EAAE;;;;;;0BAEL,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET,EAAE;;;;;;;;;;;;AAIX;GA5BwB;;QACP,yHAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;;;KAFH", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/submit/success/EditLaunchDateButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from '@/i18n/routing';\nimport { useTranslations } from 'next-intl';\n\ninterface EditLaunchDateButtonProps {\n  toolId: string;\n  toolStatus: string;\n}\n\nexport default function EditLaunchDateButton({ toolId, toolStatus }: EditLaunchDateButtonProps) {\n  const router = useRouter();\n  const t = useTranslations('submit.success');\n\n  const handleEditLaunchDate = () => {\n    // 跳转到发布日期选择页面\n    router.push(`/submit/launch-date/${toolId}`);\n  };\n\n  // 只有在 pending 或 approved 状态下才显示按钮\n  if (!['pending', 'approved'].includes(toolStatus)) {\n    return null;\n  }\n\n  return (\n    <button\n      onClick={handleEditLaunchDate}\n      className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\"\n    >\n      {t('edit_launch_date')}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,qBAAqB,EAAE,MAAM,EAAE,UAAU,EAA6B;;IAC5F,MAAM,SAAS,CAAA,GAAA,yHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,uBAAuB;QAC3B,cAAc;QACd,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ;IAC7C;IAEA,kCAAkC;IAClC,IAAI,CAAC;QAAC;QAAW;KAAW,CAAC,QAAQ,CAAC,aAAa;QACjD,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,WAAU;kBAET,EAAE;;;;;;AAGT;GAtBwB;;QACP,yHAAA,CAAA,YAAS;QACd,yMAAA,CAAA,kBAAe;;;KAFH", "debugId": null}}]}