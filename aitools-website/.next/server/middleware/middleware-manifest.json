{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3sf73lLJU2H8GXSDx5RqJMfZ441Wf8cBm7jGcmaHrCY=", "__NEXT_PREVIEW_MODE_ID": "d16a1d0c84c2386b5d1ca41455daab8d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7a239c3f6c8df89cac795d300a461c5fb2a7658f40c0c70f29692f0ac835c9e8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c53e78c964b7fae1fd58ff5e8bbb167a7dd644824940ddce71196b01ff6b1dc5"}}}, "instrumentation": null, "functions": {}}