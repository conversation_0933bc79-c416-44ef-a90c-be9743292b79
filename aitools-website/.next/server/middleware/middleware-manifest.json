{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mxs6zNlSEYfO7xo+mXV1w1Wa6Gddhn1HOLHdjI7S9Ik=", "__NEXT_PREVIEW_MODE_ID": "530910b255b6de1cb1bcfda030e8ffcc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9407f32d1c112b13ddbd38d0815afcf2a4f27704d3b7af4249a72fed4517c2cc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "24031012e1b5744d90d8a12bd47782350d6cc7ba03ec33a23ee6585790f8026e"}}}, "instrumentation": null, "functions": {}}