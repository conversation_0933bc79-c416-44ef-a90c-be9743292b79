{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "IBCKtSr50mVr5qyjqc84IbmgDrtjKtpeN9PFStgL+jE=", "__NEXT_PREVIEW_MODE_ID": "038e32aa1b75f058fde087f7f43aa8ec", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e2e502e8335271da0fad04af2fa6d097826260728508bcfd81c239dae63a2549", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cf8e6e8c32f6cf8194cc497318f2f862fbdbd4b852e4656318ced1a3f364aa32"}}}, "instrumentation": null, "functions": {}}