module.exports = {

"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[project]/src/constants/pricing.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 统一的价格配置文件
 * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中
 */ // 基础价格配置
__turbopack_context__.s({
    "LAUNCH_OPTIONS": (()=>LAUNCH_OPTIONS),
    "PRICING_CONFIG": (()=>PRICING_CONFIG),
    "TOOL_PRICING_FORM_OPTIONS": (()=>TOOL_PRICING_FORM_OPTIONS),
    "TOOL_PRICING_OPTIONS": (()=>TOOL_PRICING_OPTIONS),
    "TOOL_PRICING_TYPES": (()=>TOOL_PRICING_TYPES),
    "formatPrice": (()=>formatPrice),
    "formatStripeAmount": (()=>formatStripeAmount),
    "getPricingConfig": (()=>getPricingConfig),
    "getToolPricingColor": (()=>getToolPricingColor),
    "getToolPricingText": (()=>getToolPricingText)
});
const PRICING_CONFIG = {
    // 优先发布服务价格
    PRIORITY_LAUNCH: {
        // 显示价格（元）
        displayPrice: 19.9,
        // Stripe价格（分为单位）
        stripeAmount: 1990,
        // 货币
        currency: 'USD',
        // Stripe货币代码（小写）
        stripeCurrency: 'usd',
        // 产品名称
        productName: 'AI工具优先发布服务',
        // 产品描述
        description: '让您的AI工具获得优先审核和推荐位置',
        // 功能特性
        features: [
            '可选择任意发布日期',
            '优先审核处理',
            '首页推荐位置',
            '专属客服支持'
        ]
    },
    // 免费发布配置
    FREE_LAUNCH: {
        displayPrice: 0,
        stripeAmount: 0,
        currency: 'USD',
        stripeCurrency: 'usd',
        productName: '免费发布服务',
        description: '选择一个月后的任意发布日期',
        features: [
            '免费提交审核',
            '发布日期：一个月后起',
            '正常审核流程',
            '标准展示位置'
        ]
    }
};
const LAUNCH_OPTIONS = [
    {
        id: 'free',
        title: '免费发布',
        description: PRICING_CONFIG.FREE_LAUNCH.description,
        price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,
        features: PRICING_CONFIG.FREE_LAUNCH.features
    },
    {
        id: 'paid',
        title: '优先发布',
        description: PRICING_CONFIG.PRIORITY_LAUNCH.description,
        price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,
        features: PRICING_CONFIG.PRIORITY_LAUNCH.features,
        recommended: true
    }
];
const TOOL_PRICING_TYPES = {
    FREE: {
        value: 'free',
        label: '免费',
        color: 'bg-green-100 text-green-800'
    },
    FREEMIUM: {
        value: 'freemium',
        label: '免费增值',
        color: 'bg-blue-100 text-blue-800'
    },
    PAID: {
        value: 'paid',
        label: '付费',
        color: 'bg-orange-100 text-orange-800'
    }
};
const TOOL_PRICING_OPTIONS = [
    {
        value: '',
        label: '所有价格'
    },
    {
        value: TOOL_PRICING_TYPES.FREE.value,
        label: TOOL_PRICING_TYPES.FREE.label
    },
    {
        value: TOOL_PRICING_TYPES.FREEMIUM.value,
        label: TOOL_PRICING_TYPES.FREEMIUM.label
    },
    {
        value: TOOL_PRICING_TYPES.PAID.value,
        label: TOOL_PRICING_TYPES.PAID.label
    }
];
const TOOL_PRICING_FORM_OPTIONS = [
    {
        value: TOOL_PRICING_TYPES.FREE.value,
        label: TOOL_PRICING_TYPES.FREE.label
    },
    {
        value: TOOL_PRICING_TYPES.FREEMIUM.value,
        label: TOOL_PRICING_TYPES.FREEMIUM.label
    },
    {
        value: TOOL_PRICING_TYPES.PAID.value,
        label: TOOL_PRICING_TYPES.PAID.label
    }
];
const getPricingConfig = (optionId)=>{
    return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;
};
const getToolPricingColor = (pricing)=>{
    switch(pricing){
        case TOOL_PRICING_TYPES.FREE.value:
            return TOOL_PRICING_TYPES.FREE.color;
        case TOOL_PRICING_TYPES.FREEMIUM.value:
            return TOOL_PRICING_TYPES.FREEMIUM.color;
        case TOOL_PRICING_TYPES.PAID.value:
            return TOOL_PRICING_TYPES.PAID.color;
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
const getToolPricingText = (pricing)=>{
    switch(pricing){
        case TOOL_PRICING_TYPES.FREE.value:
            return TOOL_PRICING_TYPES.FREE.label;
        case TOOL_PRICING_TYPES.FREEMIUM.value:
            return TOOL_PRICING_TYPES.FREEMIUM.label;
        case TOOL_PRICING_TYPES.PAID.value:
            return TOOL_PRICING_TYPES.PAID.label;
        default:
            return pricing;
    }
};
const formatPrice = (price)=>{
    return price === 0 ? '免费' : `¥${price}`;
};
const formatStripeAmount = (amount, currency = 'cny')=>{
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: currency.toUpperCase(),
        minimumFractionDigits: 2
    }).format(amount / 100);
};
}}),
"[project]/src/lib/stripe.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STRIPE_CONFIG": (()=>STRIPE_CONFIG),
    "confirmPaymentIntent": (()=>confirmPaymentIntent),
    "constructWebhookEvent": (()=>constructWebhookEvent),
    "createPaymentIntent": (()=>createPaymentIntent),
    "createStripeCustomer": (()=>createStripeCustomer),
    "formatAmount": (()=>formatAmount),
    "getOrCreateStripeCustomer": (()=>getOrCreateStripeCustomer),
    "getStripePublishableKey": (()=>getStripePublishableKey),
    "stripe": (()=>stripe),
    "verifyWebhookSignature": (()=>verifyWebhookSignature)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$stripe$2f$esm$2f$stripe$2e$esm$2e$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/stripe/esm/stripe.esm.node.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/pricing.ts [app-route] (ecmascript)");
;
;
const stripe = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$stripe$2f$esm$2f$stripe$2e$esm$2e$node$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"](process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2024-12-18.acacia',
    typescript: true
});
const getStripePublishableKey = ()=>{
    return "TURBOPACK compile-time value", "pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM";
};
const STRIPE_CONFIG = {
    // 优先发布服务产品
    PRIORITY_LAUNCH: {
        productName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.productName,
        price: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.stripeAmount,
        currency: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.stripeCurrency,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.description,
        features: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.features
    }
};
async function createPaymentIntent(amount, currency = 'cny', metadata = {}) {
    try {
        const paymentIntent = await stripe.paymentIntents.create({
            amount,
            currency,
            metadata,
            automatic_payment_methods: {
                enabled: true
            }
        });
        return paymentIntent;
    } catch (error) {
        console.error('Error creating payment intent:', error);
        throw new Error('Failed to create payment intent');
    }
}
async function confirmPaymentIntent(paymentIntentId) {
    try {
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
        return paymentIntent;
    } catch (error) {
        console.error('Error confirming payment intent:', error);
        throw new Error('Failed to confirm payment intent');
    }
}
async function createStripeCustomer(email, name, metadata = {}) {
    try {
        const customer = await stripe.customers.create({
            email,
            name,
            metadata
        });
        return customer;
    } catch (error) {
        console.error('Error creating Stripe customer:', error);
        throw new Error('Failed to create customer');
    }
}
async function getOrCreateStripeCustomer(email, name, metadata = {}) {
    try {
        // 首先尝试查找现有客户
        const existingCustomers = await stripe.customers.list({
            email,
            limit: 1
        });
        if (existingCustomers.data.length > 0) {
            return existingCustomers.data[0];
        }
        // 如果没有找到，创建新客户
        return await createStripeCustomer(email, name, metadata);
    } catch (error) {
        console.error('Error getting or creating Stripe customer:', error);
        throw new Error('Failed to get or create customer');
    }
}
function constructWebhookEvent(payload, signature, secret) {
    try {
        return stripe.webhooks.constructEvent(payload, signature, secret);
    } catch (error) {
        console.error('Error constructing webhook event:', error);
        throw new Error('Invalid webhook signature');
    }
}
function formatAmount(amount, currency = 'cny') {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: currency.toUpperCase(),
        minimumFractionDigits: 2
    }).format(amount / 100);
}
function verifyWebhookSignature(payload, signature) {
    try {
        const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
        if (!webhookSecret) {
            throw new Error('Webhook secret not configured');
        }
        constructWebhookEvent(payload, signature, webhookSecret);
        return true;
    } catch (error) {
        console.error('Webhook signature verification failed:', error);
        return false;
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c0ad2f58._.js.map