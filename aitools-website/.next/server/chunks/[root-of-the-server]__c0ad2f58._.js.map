{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts"], "sourcesContent": ["import Stripe from 'stripe';\nimport { PRICING_CONFIG } from '@/constants/pricing';\n\n// 服务端Stripe实例\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2024-12-18.acacia',\n  typescript: true,\n});\n\n// 客户端Stripe配置\nexport const getStripePublishableKey = () => {\n  return process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!;\n};\n\n// Stripe产品和价格配置（使用统一配置）\nexport const STRIPE_CONFIG = {\n  // 优先发布服务产品\n  PRIORITY_LAUNCH: {\n    productName: PRICING_CONFIG.PRIORITY_LAUNCH.productName,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount,\n    currency: PRICING_CONFIG.PRIORITY_LAUNCH.stripeCurrency,\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features\n  }\n};\n\n// 创建支付意图\nexport async function createPaymentIntent(\n  amount: number,\n  currency: string = 'cny',\n  metadata: Record<string, string> = {}\n): Promise<Stripe.PaymentIntent> {\n  try {\n    const paymentIntent = await stripe.paymentIntents.create({\n      amount,\n      currency,\n      metadata,\n      automatic_payment_methods: {\n        enabled: true,\n      },\n    });\n\n    return paymentIntent;\n  } catch (error) {\n    console.error('Error creating payment intent:', error);\n    throw new Error('Failed to create payment intent');\n  }\n}\n\n// 确认支付意图\nexport async function confirmPaymentIntent(\n  paymentIntentId: string\n): Promise<Stripe.PaymentIntent> {\n  try {\n    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);\n    return paymentIntent;\n  } catch (error) {\n    console.error('Error confirming payment intent:', error);\n    throw new Error('Failed to confirm payment intent');\n  }\n}\n\n// 创建客户\nexport async function createStripeCustomer(\n  email: string,\n  name?: string,\n  metadata: Record<string, string> = {}\n): Promise<Stripe.Customer> {\n  try {\n    const customer = await stripe.customers.create({\n      email,\n      name,\n      metadata,\n    });\n\n    return customer;\n  } catch (error) {\n    console.error('Error creating Stripe customer:', error);\n    throw new Error('Failed to create customer');\n  }\n}\n\n// 获取或创建客户\nexport async function getOrCreateStripeCustomer(\n  email: string,\n  name?: string,\n  metadata: Record<string, string> = {}\n): Promise<Stripe.Customer> {\n  try {\n    // 首先尝试查找现有客户\n    const existingCustomers = await stripe.customers.list({\n      email,\n      limit: 1,\n    });\n\n    if (existingCustomers.data.length > 0) {\n      return existingCustomers.data[0];\n    }\n\n    // 如果没有找到，创建新客户\n    return await createStripeCustomer(email, name, metadata);\n  } catch (error) {\n    console.error('Error getting or creating Stripe customer:', error);\n    throw new Error('Failed to get or create customer');\n  }\n}\n\n// 处理Webhook事件\nexport function constructWebhookEvent(\n  payload: string | Buffer,\n  signature: string,\n  secret: string\n): Stripe.Event {\n  try {\n    return stripe.webhooks.constructEvent(payload, signature, secret);\n  } catch (error) {\n    console.error('Error constructing webhook event:', error);\n    throw new Error('Invalid webhook signature');\n  }\n}\n\n// 格式化金额显示（使用统一配置）\nexport function formatAmount(amount: number, currency: string = 'cny'): string {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n}\n\n// 验证Webhook签名\nexport function verifyWebhookSignature(\n  payload: string | Buffer,\n  signature: string\n): boolean {\n  try {\n    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n    if (!webhookSecret) {\n      throw new Error('Webhook secret not configured');\n    }\n\n    constructWebhookEvent(payload, signature, webhookSecret);\n    return true;\n  } catch (error) {\n    console.error('Webhook signature verification failed:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAGO,MAAM,SAAS,IAAI,wJAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAG;IAC/D,YAAY;IACZ,YAAY;AACd;AAGO,MAAM,0BAA0B;IACrC;AACF;AAGO,MAAM,gBAAgB;IAC3B,WAAW;IACX,iBAAiB;QACf,aAAa,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,WAAW;QACvD,OAAO,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY;QAClD,UAAU,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,cAAc;QACvD,aAAa,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,WAAW;QACvD,UAAU,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,QAAQ;IACnD;AACF;AAGO,eAAe,oBACpB,MAAc,EACd,WAAmB,KAAK,EACxB,WAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,gBAAgB,MAAM,OAAO,cAAc,CAAC,MAAM,CAAC;YACvD;YACA;YACA;YACA,2BAA2B;gBACzB,SAAS;YACX;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,qBACpB,eAAuB;IAEvB,IAAI;QACF,MAAM,gBAAgB,MAAM,OAAO,cAAc,CAAC,QAAQ,CAAC;QAC3D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,qBACpB,KAAa,EACb,IAAa,EACb,WAAmC,CAAC,CAAC;IAErC,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,SAAS,CAAC,MAAM,CAAC;YAC7C;YACA;YACA;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,0BACpB,KAAa,EACb,IAAa,EACb,WAAmC,CAAC,CAAC;IAErC,IAAI;QACF,aAAa;QACb,MAAM,oBAAoB,MAAM,OAAO,SAAS,CAAC,IAAI,CAAC;YACpD;YACA,OAAO;QACT;QAEA,IAAI,kBAAkB,IAAI,CAAC,MAAM,GAAG,GAAG;YACrC,OAAO,kBAAkB,IAAI,CAAC,EAAE;QAClC;QAEA,eAAe;QACf,OAAO,MAAM,qBAAqB,OAAO,MAAM;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,SAAS,sBACd,OAAwB,EACxB,SAAiB,EACjB,MAAc;IAEd,IAAI;QACF,OAAO,OAAO,QAAQ,CAAC,cAAc,CAAC,SAAS,WAAW;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,SAAS,aAAa,MAAc,EAAE,WAAmB,KAAK;IACnE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB;AAGO,SAAS,uBACd,OAAwB,EACxB,SAAiB;IAEjB,IAAI;QACF,MAAM,gBAAgB,QAAQ,GAAG,CAAC,qBAAqB;QACvD,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,sBAAsB,SAAS,WAAW;QAC1C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACT;AACF", "debugId": null}}]}