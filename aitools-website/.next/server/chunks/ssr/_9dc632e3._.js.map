{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/test-success/page.tsx"], "sourcesContent": ["import { getTranslations } from 'next-intl/server';\nimport { Locale } from '@/i18n/config';\n\ninterface PageProps {\n  params: Promise<{ locale: Locale }>;\n}\n\nexport default async function TestSuccessPage({ params }: PageProps) {\n  const { locale } = await params;\n  const t = await getTranslations('submit.success');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-2xl mx-auto px-4\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h1 className=\"text-3xl font-bold text-center mb-8\">\n            Submit Success Page Translation Test\n          </h1>\n          \n          <div className=\"space-y-6\">\n            <div>\n              <h2 className=\"text-xl font-semibold mb-4\">Current Locale: {locale}</h2>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Success Titles:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Payment Success:</strong> {t('payment_success_title')}</p>\n                <p><strong>Submit Success:</strong> {t('submit_success_title')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Descriptions:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Payment:</strong> {t('payment_success_desc')}</p>\n                <p><strong>Submit:</strong> {t('submit_success_desc')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Tool Info Labels:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Tool Info Title:</strong> {t('tool_info_title')}</p>\n                <p><strong>Tool Name:</strong> {t('tool_name_label')}</p>\n                <p><strong>Current Status:</strong> {t('current_status_label')}</p>\n                <p><strong>Launch Option:</strong> {t('launch_option_label')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Status Values:</h3>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <p><strong>Draft:</strong> {t('status_draft')}</p>\n                  <p><strong>Pending:</strong> {t('status_pending')}</p>\n                </div>\n                <div>\n                  <p><strong>Approved:</strong> {t('status_approved')}</p>\n                  <p><strong>Rejected:</strong> {t('status_rejected')}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Launch Options:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Paid:</strong> {t('launch_option_paid')}</p>\n                <p><strong>Free:</strong> {t('launch_option_free')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Service Titles:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Premium Service:</strong> {t('premium_service_title')}</p>\n                <p><strong>Free Service:</strong> {t('free_service_title')}</p>\n                <p><strong>Launch Date Management:</strong> {t('launch_date_management_title')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Premium Features:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Priority Review:</strong> {t('premium_features.priority_review')}</p>\n                <p><strong>Homepage Featured:</strong> {t('premium_features.homepage_featured')}</p>\n                <p><strong>Custom Launch Date:</strong> {t('premium_features.custom_launch_date')}</p>\n                <p><strong>Dedicated Support:</strong> {t('premium_features.dedicated_support')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Free Features:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Standard Review:</strong> {t('free_features.standard_review')}</p>\n                <p><strong>Flexible Date:</strong> {t('free_features.flexible_date')}</p>\n                <p><strong>Standard Position:</strong> {t('free_features.standard_position')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Help & Actions:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Help Title:</strong> {t('help_title')}</p>\n                <p><strong>Help Description:</strong> {t('help_desc')}</p>\n                <p><strong>Contact Email:</strong> {t('contact_email')}</p>\n                <p><strong>View Submissions:</strong> {t('view_submissions')}</p>\n                <p><strong>Back to Home:</strong> {t('back_to_home')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">Date & Tips:</h3>\n              <div className=\"space-y-2\">\n                <p><strong>Current Launch Date:</strong> {t('current_launch_date_label')}</p>\n                <p><strong>Launch Date Tip:</strong> {t('launch_date_tip')}</p>\n                <p><strong>Launch Date Tip Free:</strong> {t('launch_date_tip_free')}</p>\n                <p><strong>Edit Launch Date:</strong> {t('edit_launch_date')}</p>\n              </div>\n            </div>\n\n            <div className=\"border-t pt-6 text-center\">\n              <div className=\"space-x-4\">\n                <a \n                  href=\"/en/test-success\" \n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-lg inline-block\"\n                >\n                  English Version\n                </a>\n                <a \n                  href=\"/zh/test-success\" \n                  className=\"bg-green-600 text-white px-4 py-2 rounded-lg inline-block\"\n                >\n                  中文版本\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAOe,eAAe,gBAAgB,EAAE,MAAM,EAAa;IACjE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAIpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CACC,cAAA,8OAAC;oCAAG,WAAU;;wCAA6B;wCAAiB;;;;;;;;;;;;0CAG9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAE,EAAE;;;;;;;0DACxC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAwB;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAiB;oDAAE,EAAE;;;;;;;0DAChC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAgB;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAInC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAE,EAAE;;;;;;;0DACxC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAmB;oDAAE,EAAE;;;;;;;0DAClC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAwB;oDAAE,EAAE;;;;;;;0DACvC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAuB;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAe;4DAAE,EAAE;;;;;;;kEAC9B,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAiB;4DAAE,EAAE;;;;;;;;;;;;;0DAElC,8OAAC;;kEACC,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAkB;4DAAE,EAAE;;;;;;;kEACjC,8OAAC;;0EAAE,8OAAC;0EAAO;;;;;;4DAAkB;4DAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,EAAE;;;;;;;0DAC7B,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAIjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAE,EAAE;;;;;;;0DACxC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAsB;oDAAE,EAAE;;;;;;;0DACrC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAgC;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAE,EAAE;;;;;;;0DACxC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA2B;oDAAE,EAAE;;;;;;;0DAC1C,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA4B;oDAAE,EAAE;;;;;;;0DAC3C,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA2B;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAI9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAE,EAAE;;;;;;;0DACxC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAuB;oDAAE,EAAE;;;;;;;0DACtC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA2B;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAI9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAoB;oDAAE,EAAE;;;;;;;0DACnC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA0B;oDAAE,EAAE;;;;;;;0DACzC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAuB;oDAAE,EAAE;;;;;;;0DACtC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA0B;oDAAE,EAAE;;;;;;;0DACzC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAsB;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA6B;oDAAE,EAAE;;;;;;;0DAC5C,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAyB;oDAAE,EAAE;;;;;;;0DACxC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA8B;oDAAE,EAAE;;;;;;;0DAC7C,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA0B;oDAAE,EAAE;;;;;;;;;;;;;;;;;;;0CAI7C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAMhG,8BAA8B;IAI5BI,SAASC;;;;;;;;;;;;;AAIX,cAAc,0CAA0C,iBAAA;IAAE,MAAA,kBAAwB;AAAsB,EAAC,IAAA,OAAA;IAAA;IAAA;QAEzG,YAAA;YAAA;YAAA,mCAA4D;gBAC5D,OAAO,KAAA;oBAAMG,cAAc;oBAAA,EAAIX,mBAAmB;4BAChDY,QAAAA;4BAAAA,GAAY;4BAAA;iCACVC,MAAMZ,UAAUa,QAAQ;sCACxBC,IAAAA,CAAM,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;+BAC<PERSON>,UAAU;;yBACV,2CAA2C;8BAC3CC,IAAAA,CAAAA;oBAAAA;iBAAAA,CAAY;;iBACZC,UAAU;sBACVC,IAAAA,CAAAA,GAAU;gBAAA,CAAE,SAAA;oBAAA,IAAA;oBAAA;iBAAA;;WACd;;KACAC,UAAU;cACRC,IAAAA;YAAAA,GAAYnB,GAAAA;gBACd,OAAA,QAAA;wBAAA;4BACA,KAAA,CAAA,GAAA,4MAAA,CAAA,sBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA", "ignoreList": [0], "debugId": null}}]}