{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport dbConnect from './mongodb';\nimport User from '../models/User';\nimport { getNextAuthUrl } from './env';\n\nexport const authOptions: NextAuthOptions = {\n  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略\n  // adapter: MongoDBAdapter(client),\n\n  // 动态配置基础URL，支持不同环境\n  // NextAuth会自动从环境变量或请求头中检测URL，但我们也可以显式设置\n  // 在生产环境中，这将被环境变量覆盖\n  ...(process.env.NODE_ENV === 'development' && {\n    // 仅在开发环境中设置，生产环境让NextAuth自动检测\n    // 这样可以支持不同的开发端口\n  }),\n\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      id: 'email-code',\n      name: 'Email Code',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        code: { label: 'Code', type: 'text' },\n        token: { label: 'Token', type: 'text' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.code || !credentials?.token) {\n          return null;\n        }\n\n        try {\n          await dbConnect();\n\n          // 查找用户\n          const user = await User.findOne({\n            email: credentials.email.toLowerCase(),\n            emailVerificationExpires: { $gt: new Date() }\n          });\n\n          if (!user) {\n            return null;\n          }\n\n          // 验证token和验证码\n          const storedData = user.emailVerificationToken;\n          if (!storedData || !storedData.includes(':')) {\n            return null;\n          }\n\n          const [storedToken, storedCode] = storedData.split(':');\n\n          if (storedToken !== credentials.token || storedCode !== credentials.code) {\n            return null;\n          }\n\n          // 验证成功，更新用户状态\n          user.emailVerified = true;\n          user.emailVerificationToken = undefined;\n          user.emailVerificationExpires = undefined;\n          user.lastLoginAt = new Date();\n\n          // 如果用户没有邮箱账户记录，添加一个\n          const hasEmailAccount = user.accounts.some((acc: any) => acc.provider === 'email');\n          if (!hasEmailAccount) {\n            user.accounts.push({\n              provider: 'email',\n              providerId: 'email',\n              providerAccountId: user.email,\n            });\n          }\n\n          await user.save();\n\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            name: user.name,\n            image: user.avatar,\n            role: user.role,\n          };\n        } catch (error) {\n          console.error('Email code authorization error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      // 对于credentials provider，用户已经在authorize中处理过了\n      if (account?.provider === 'email-code') {\n        return true;\n      }\n\n      await dbConnect();\n\n      try {\n        // 查找或创建用户（仅用于OAuth providers）\n        let existingUser = await User.findOne({ email: user.email });\n\n        if (!existingUser) {\n          // 创建新用户\n          existingUser = new User({\n            email: user.email,\n            name: user.name || profile?.name || 'User',\n            avatar: user.image || profile?.image,\n            emailVerified: true, // OAuth登录默认已验证\n            lastLoginAt: new Date(),\n          });\n          await existingUser.save();\n        } else {\n          // 更新最后登录时间\n          existingUser.lastLoginAt = new Date();\n          await existingUser.save();\n        }\n\n        // 添加或更新账户信息\n        if (account && account.provider !== 'email-code') {\n          existingUser.addAccount({\n            provider: account.provider as 'google' | 'github',\n            providerId: account.provider,\n            providerAccountId: account.providerAccountId || account.id || '',\n            accessToken: account.access_token,\n            refreshToken: account.refresh_token,\n            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,\n          });\n          await existingUser.save();\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Sign in error:', error);\n        return false;\n      }\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        // 对于credentials provider，user对象已经包含了我们需要的信息\n        token.userId = user.id;\n        token.role = (user as any).role || 'user';\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        (session.user as any).id = token.userId as string;\n        (session.user as any).role = token.role as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,cAA+B;IAC1C,iDAAiD;IACjD,mCAAmC;IAEnC,mBAAmB;IACnB,wCAAwC;IACxC,mBAAmB;IACnB,GAAI,oDAAyB,iBAAiB;IAG9C,CAAC;IAED,WAAW;QACT,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,wJAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,MAAM;oBAAE,OAAO;oBAAQ,MAAM;gBAAO;gBACpC,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;YACxC;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;oBACpE,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;oBAEd,OAAO;oBACP,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,YAAY,KAAK,CAAC,WAAW;wBACpC,0BAA0B;4BAAE,KAAK,IAAI;wBAAO;oBAC9C;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,cAAc;oBACd,MAAM,aAAa,KAAK,sBAAsB;oBAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM;wBAC5C,OAAO;oBACT;oBAEA,MAAM,CAAC,aAAa,WAAW,GAAG,WAAW,KAAK,CAAC;oBAEnD,IAAI,gBAAgB,YAAY,KAAK,IAAI,eAAe,YAAY,IAAI,EAAE;wBACxE,OAAO;oBACT;oBAEA,cAAc;oBACd,KAAK,aAAa,GAAG;oBACrB,KAAK,sBAAsB,GAAG;oBAC9B,KAAK,wBAAwB,GAAG;oBAChC,KAAK,WAAW,GAAG,IAAI;oBAEvB,oBAAoB;oBACpB,MAAM,kBAAkB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK;oBAC1E,IAAI,CAAC,iBAAiB;wBACpB,KAAK,QAAQ,CAAC,IAAI,CAAC;4BACjB,UAAU;4BACV,YAAY;4BACZ,mBAAmB,KAAK,KAAK;wBAC/B;oBACF;oBAEA,MAAM,KAAK,IAAI;oBAEf,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,MAAM;wBAClB,MAAM,KAAK,IAAI;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,6CAA6C;YAC7C,IAAI,SAAS,aAAa,cAAc;gBACtC,OAAO;YACT;YAEA,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;YAEd,IAAI;gBACF,8BAA8B;gBAC9B,IAAI,eAAe,MAAM,qHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,KAAK,KAAK;gBAAC;gBAE1D,IAAI,CAAC,cAAc;oBACjB,QAAQ;oBACR,eAAe,IAAI,qHAAA,CAAA,UAAI,CAAC;wBACtB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;wBACpC,QAAQ,KAAK,KAAK,IAAI,SAAS;wBAC/B,eAAe;wBACf,aAAa,IAAI;oBACnB;oBACA,MAAM,aAAa,IAAI;gBACzB,OAAO;oBACL,WAAW;oBACX,aAAa,WAAW,GAAG,IAAI;oBAC/B,MAAM,aAAa,IAAI;gBACzB;gBAEA,YAAY;gBACZ,IAAI,WAAW,QAAQ,QAAQ,KAAK,cAAc;oBAChD,aAAa,UAAU,CAAC;wBACtB,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,QAAQ;wBAC5B,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,EAAE,IAAI;wBAC9D,aAAa,QAAQ,YAAY;wBACjC,cAAc,QAAQ,aAAa;wBACnC,WAAW,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,QAAQ;oBACxE;oBACA,MAAM,aAAa,IAAI;gBACzB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;YACT;QACF;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,KAAK,EAAE;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,IAAI;YACrC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,MAAM;gBACtC,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts"], "sourcesContent": ["// 国际化分类配置文件\n// 支持多语言的AI工具分类配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 分类的基础配置（不包含翻译文本）\nexport const CATEGORY_BASE_CONFIGS = [\n  {\n    slug: 'text-generation',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'data-analysis',\n    icon: '📊',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'audio-processing',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'education',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'marketing',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'productivity',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'customer-service',\n    icon: '🎧',\n    color: '#F59E0B'\n  }\n];\n\n// 客户端钩子：获取国际化的分类配置\nexport function useCategoryConfigs(): CategoryConfig[] {\n  const t = useTranslations('categories');\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 客户端钩子：获取分类选项（用于下拉框等）\nexport function useCategoryOptions(): CategoryOption[] {\n  const configs = useCategoryConfigs();\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 客户端钩子：获取包含\"所有分类\"选项的分类选项\nexport function useCategoryOptionsWithAll(): CategoryOption[] {\n  const t = useTranslations('categories');\n  const options = useCategoryOptions();\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 客户端钩子：获取分类名称\nexport function useCategoryName(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 客户端钩子：获取分类描述\nexport function useCategoryDescription(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取国际化的分类配置\nexport async function getCategoryConfigs(locale?: string): Promise<CategoryConfig[]> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 服务器端函数：获取分类选项\nexport async function getCategoryOptions(locale?: string): Promise<CategoryOption[]> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 服务器端函数：获取包含\"所有分类\"选项的分类选项\nexport async function getCategoryOptionsWithAll(locale?: string): Promise<CategoryOption[]> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  const options = await getCategoryOptions(locale);\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 服务器端函数：获取分类名称\nexport async function getCategoryName(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 服务器端函数：获取分类描述\nexport async function getCategoryDescription(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取分类配置\nexport async function getCategoryConfig(slug: string, locale?: string): Promise<CategoryConfig | undefined> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.find(config => config.slug === slug);\n}\n\n// 验证分类是否存在的辅助函数\nexport function isValidCategory(slug: string): boolean {\n  return CATEGORY_BASE_CONFIGS.some(config => config.slug === slug);\n}\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map(config => config.slug);\n\n// 分类元数据映射（slug -> 基础配置）\nexport const CATEGORY_BASE_METADATA: Record<string, typeof CATEGORY_BASE_CONFIGS[0]> = \n  CATEGORY_BASE_CONFIGS.reduce((acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  }, {} as Record<string, typeof CATEGORY_BASE_CONFIGS[0]>);\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;;;;;;AAEjB;AACA;;;AAgBO,MAAM,wBAAwB;IACnC;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,UAAU;IAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU;IAEhB,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,SAAS,gBAAgB,IAAY;IAC1C,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,SAAS,uBAAuB,IAAY;IACjD,MAAM,IAAI,CAAA,GAAA,8OAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAElE,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,eAAe,0BAA0B,MAAe;IAC7D,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,MAAM,UAAU,MAAM,mBAAmB;IAEzC,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,eAAe,gBAAgB,IAAY,EAAE,MAAe;IACjE,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,eAAe,uBAAuB,IAAY,EAAE,MAAe;IACxE,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,kBAAkB,IAAY,EAAE,MAAe;IACnE,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAGO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,MAAM,iBAAiB,sBAAsB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;AAGtE,MAAM,yBACX,sBAAsB,MAAM,CAAC,CAAC,KAAK;IACjC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { CATEGORY_SLUGS } from '@/constants/categories-i18n';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  launchDate?: Date; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    maxlength: [100, 'Tool name cannot exceed 100 characters']\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Tagline cannot exceed 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [2000, 'Long description cannot exceed 2000 characters']\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: CATEGORY_SLUGS\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Pricing details cannot exceed 500 characters']\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  launchDate: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ launchDate: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAuC;IAC1D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAiD;IACrE;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM,sIAAA,CAAA,iBAAc;IACtB;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAA+C;IAClE;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;SAAW;QAClD,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,YAAY,CAAC;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/submit/success/ActionButtons.tsx"], "sourcesContent": ["import { getTranslations } from 'next-intl/server';\nimport { <PERSON> } from '@/i18n/routing';\n\ninterface ActionButtonsProps {\n  locale: string;\n}\n\nexport default async function ActionButtons({ locale }: ActionButtonsProps) {\n  const t = await getTranslations('submit.success');\n\n  return (\n    <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n      <Link\n        href=\"/profile/submitted\"\n        className=\"bg-blue-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors\"\n      >\n        {t('view_submissions')}\n      </Link>\n      <Link\n        href=\"/\"\n        className=\"bg-gray-600 text-white px-6 py-3 rounded-lg font-medium text-center hover:bg-gray-700 transition-colors\"\n      >\n        {t('back_to_home')}\n      </Link>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMe,eAAe,cAAc,EAAE,MAAM,EAAsB;IACxE,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sHAAA,CAAA,OAAI;gBACH,MAAK;gBACL,WAAU;0BAET,EAAE;;;;;;0BAEL,8OAAC,sHAAA,CAAA,OAAI;gBACH,MAAK;gBACL,WAAU;0BAET,EAAE;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/submit/success/EditLaunchDateButton.tsx"], "sourcesContent": ["import { getTranslations } from 'next-intl/server';\nimport { Link } from '@/i18n/routing';\n\ninterface EditLaunchDateButtonProps {\n  toolId: string;\n  toolStatus: string;\n}\n\nexport default async function EditLaunchDateButton({ toolId, toolStatus }: EditLaunchDateButtonProps) {\n  const t = await getTranslations('submit.success');\n\n  // 只有在 pending 或 approved 状态下才显示按钮\n  if (!['pending', 'approved'].includes(toolStatus)) {\n    return null;\n  }\n\n  return (\n    <Link\n      href={`/submit/edit-launch-date/${toolId}`}\n      className=\"bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors\"\n    >\n      {t('edit_launch_date')}\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOe,eAAe,qBAAqB,EAAE,MAAM,EAAE,UAAU,EAA6B;IAClG,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,kCAAkC;IAClC,IAAI,CAAC;QAAC;QAAW;KAAW,CAAC,QAAQ,CAAC,aAAa;QACjD,OAAO;IACT;IAEA,qBACE,8OAAC,sHAAA,CAAA,OAAI;QACH,MAAM,CAAC,yBAAyB,EAAE,QAAQ;QAC1C,WAAU;kBAET,EAAE;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 1019, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/submit/success/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\nimport User from '@/models/User';\nimport mongoose from 'mongoose';\nimport { CheckCircle, CreditCard, Calendar } from 'lucide-react';\nimport { getTranslations } from 'next-intl/server';\nimport { Locale } from '@/i18n/config';\nimport ActionButtons from './ActionButtons';\nimport EditLaunchDateButton from './EditLaunchDateButton';\n\n// 格式化日期函数，确保服务器端和客户端一致性\nfunction formatDate(dateString: string, locale: string): string {\n  const date = new Date(dateString);\n  if (locale === 'zh') {\n    return date.toLocaleDateString('zh-CN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  } else {\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n}\n\ninterface PageProps {\n  params: Promise<{ locale: Locale }>;\n  searchParams: Promise<{ toolId?: string; paid?: string }>;\n}\n\nasync function getToolData(toolId: string, userEmail: string) {\n  try {\n    await dbConnect();\n\n    if (!mongoose.Types.ObjectId.isValid(toolId)) {\n      return null;\n    }\n\n    const user = await User.findOne({ email: userEmail });\n    if (!user) {\n      return null;\n    }\n\n    const tool = await Tool.findById(toolId);\n    if (!tool) {\n      return null;\n    }\n\n    // 检查工具所有权\n    if (!tool.submittedBy || tool.submittedBy.toString() !== user._id.toString()) {\n      return null;\n    }\n\n    return {\n      _id: tool._id.toString(),\n      name: tool.name,\n      status: tool.status,\n      launchOption: tool.launchOption,\n      selectedLaunchDate: tool.selectedLaunchDate ? tool.selectedLaunchDate.toISOString() : null,\n    };\n  } catch (error) {\n    console.error('Failed to fetch tool:', error);\n    return null;\n  }\n}\n\nexport default async function SubmitSuccessPage({ params, searchParams }: PageProps) {\n  const session = await getServerSession(authOptions);\n  const { locale } = await params;\n  const { toolId, paid } = await searchParams;\n\n  // 获取翻译\n  const t = await getTranslations('submit.success');\n\n  // 检查用户是否已登录\n  if (!session?.user?.email) {\n    redirect('/');\n  }\n\n  // 检查是否有工具ID\n  if (!toolId) {\n    redirect('/');\n  }\n\n  const isPaid = paid === 'true';\n\n  // 获取工具数据\n  const tool = await getToolData(toolId, session.user.email);\n\n  if (!tool) {\n    redirect('/');\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Success Header */}\n      <div className=\"text-center mb-8\">\n        <div className=\"flex justify-center mb-4\">\n          <div className=\"bg-green-100 rounded-full p-3\">\n            <CheckCircle className=\"h-12 w-12 text-green-600\" />\n          </div>\n        </div>\n\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          {isPaid ? t('payment_success_title') : t('submit_success_title')}\n        </h1>\n\n        <p className=\"text-lg text-gray-600\">\n          {isPaid ? t('payment_success_desc') : t('submit_success_desc')}\n        </p>\n      </div>\n\n      {/* Tool Info */}\n      <div className=\"bg-white border border-gray-200 rounded-lg p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">{t('tool_info_title')}</h2>\n        <div className=\"space-y-3\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('tool_name_label')}</span>\n            <span className=\"font-medium\">{tool.name}</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('current_status_label')}</span>\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n              tool.status === 'draft' ? 'bg-gray-100 text-gray-800' :\n              tool.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n              tool.status === 'approved' ? 'bg-green-100 text-green-800' :\n              'bg-red-100 text-red-800'\n            }`}>\n              {tool.status === 'draft' ? t('status_draft') :\n               tool.status === 'pending' ? t('status_pending') :\n               tool.status === 'approved' ? t('status_approved') : t('status_rejected')}\n            </span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">{t('launch_option_label')}</span>\n            <span className=\"font-medium\">\n              {tool.launchOption === 'paid' ? t('launch_option_paid') : t('launch_option_free')}\n            </span>\n          </div>\n          {tool.selectedLaunchDate && (\n            <div className=\"flex justify-between\">\n              <span className=\"text-gray-600\">{t('planned_launch_date_label')}</span>\n              <span className=\"font-medium\">\n                {formatDate(tool.selectedLaunchDate, locale)}\n              </span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Launch Date Management - 所有用户都可以看到 */}\n      <div className={`${tool.launchOption === 'paid' ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'} border rounded-lg p-6 mb-8`}>\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center\">\n            {tool.launchOption === 'paid' ? (\n              <>\n                <CreditCard className=\"h-6 w-6 text-blue-600 mr-2\" />\n                <h3 className=\"text-lg font-semibold text-blue-900\">\n                  {t('premium_service_title')}\n                </h3>\n              </>\n            ) : (\n              <>\n                <Calendar className=\"h-6 w-6 text-gray-600 mr-2\" />\n                <h3 className=\"text-lg font-semibold text-gray-900\">\n                  {t('launch_date_management_title')}\n                </h3>\n              </>\n            )}\n          </div>\n\n              {/* 修改发布日期按钮 */}\n          <EditLaunchDateButton toolId={tool._id} toolStatus={tool.status} />\n        </div>\n\n        {/* 服务特性 - 只有付费用户显示 */}\n        {tool.launchOption === 'paid' && (\n          <div className=\"grid md:grid-cols-2 gap-4 mb-4\">\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-blue-800\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('premium_features.priority_review')}</span>\n              </div>\n              <div className=\"flex items-center text-blue-800\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('premium_features.homepage_featured')}</span>\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-blue-800\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('premium_features.custom_launch_date')}</span>\n              </div>\n              <div className=\"flex items-center text-blue-800\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('premium_features.dedicated_support')}</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 免费用户的服务说明 */}\n        {tool.launchOption === 'free' && (\n          <div className=\"bg-gray-100 rounded-lg p-4\">\n            <h4 className=\"font-medium text-gray-900 mb-2\">{t('free_service_title')}</h4>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-gray-700\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('free_features.standard_review')}</span>\n              </div>\n              <div className=\"flex items-center text-gray-700\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('free_features.flexible_date')}</span>\n              </div>\n              <div className=\"flex items-center text-gray-700\">\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                <span className=\"text-sm\">{t('free_features.standard_position')}</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 当前发布日期显示 */}\n        {tool.selectedLaunchDate && (\n          <div className={`bg-white rounded-lg p-4 border ${tool.launchOption === 'paid' ? 'border-blue-200' : 'border-gray-200'}`}>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <Calendar className={`h-5 w-5 mr-2 ${tool.launchOption === 'paid' ? 'text-blue-600' : 'text-gray-600'}`} />\n                <span className=\"text-sm font-medium text-gray-900\">{t('current_launch_date_label')}</span>\n              </div>\n              <span className={`text-sm font-semibold ${tool.launchOption === 'paid' ? 'text-blue-600' : 'text-gray-600'}`}>\n                {formatDate(tool.selectedLaunchDate, locale)}\n              </span>\n            </div>\n            {['pending', 'approved'].includes(tool.status) && (\n              <p className=\"text-xs text-gray-600 mt-2\">\n                {t('launch_date_tip')}\n                {tool.launchOption === 'free' && t('launch_date_tip_free')}\n              </p>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Contact Info */}\n      <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">\n          {t('help_title')}\n        </h3>\n        <p className=\"text-gray-600 mb-4\">\n          {t('help_desc')}\n        </p>\n        <div className=\"space-y-2 text-sm text-gray-600\">\n          <div>{t('contact_email')}</div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <ActionButtons locale={locale} />\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;;;;;;;;;;;;;AAEA,wBAAwB;AACxB,SAAS,WAAW,UAAkB,EAAE,MAAc;IACpD,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,WAAW,MAAM;QACnB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF,OAAO;QACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;AACF;AAOA,eAAe,YAAY,MAAc,EAAE,SAAiB;IAC1D,IAAI;QACF,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS;YAC5C,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAU;QACnD,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,UAAU;QACV,IAAI,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,QAAQ,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5E,OAAO;QACT;QAEA,OAAO;YACL,KAAK,KAAK,GAAG,CAAC,QAAQ;YACtB,MAAM,KAAK,IAAI;YACf,QAAQ,KAAK,MAAM;YACnB,cAAc,KAAK,YAAY;YAC/B,oBAAoB,KAAK,kBAAkB,GAAG,KAAK,kBAAkB,CAAC,WAAW,KAAK;QACxF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAEe,eAAe,kBAAkB,EAAE,MAAM,EAAE,YAAY,EAAa;IACjF,MAAM,UAAU,MAAM,CAAA,GAAA,6IAAA,CAAA,mBAAgB,AAAD,EAAE,kHAAA,CAAA,cAAW;IAClD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IAE/B,OAAO;IACP,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAEhC,YAAY;IACZ,IAAI,CAAC,SAAS,MAAM,OAAO;QACzB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,YAAY;IACZ,IAAI,CAAC,QAAQ;QACX,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,MAAM,SAAS,SAAS;IAExB,SAAS;IACT,MAAM,OAAO,MAAM,YAAY,QAAQ,QAAQ,IAAI,CAAC,KAAK;IAEzD,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAG,WAAU;kCACX,SAAS,EAAE,2BAA2B,EAAE;;;;;;kCAG3C,8OAAC;wBAAE,WAAU;kCACV,SAAS,EAAE,0BAA0B,EAAE;;;;;;;;;;;;0BAK5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4C,EAAE;;;;;;kCAC5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDAAe,KAAK,IAAI;;;;;;;;;;;;0CAE1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,MAAM,KAAK,UAAU,8BAC1B,KAAK,MAAM,KAAK,YAAY,kCAC5B,KAAK,MAAM,KAAK,aAAa,gCAC7B,2BACA;kDACC,KAAK,MAAM,KAAK,UAAU,EAAE,kBAC5B,KAAK,MAAM,KAAK,YAAY,EAAE,oBAC9B,KAAK,MAAM,KAAK,aAAa,EAAE,qBAAqB,EAAE;;;;;;;;;;;;0CAG3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDACb,KAAK,YAAY,KAAK,SAAS,EAAE,wBAAwB,EAAE;;;;;;;;;;;;4BAG/D,KAAK,kBAAkB,kBACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB,EAAE;;;;;;kDACnC,8OAAC;wCAAK,WAAU;kDACb,WAAW,KAAK,kBAAkB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAW,GAAG,KAAK,YAAY,KAAK,SAAS,+BAA+B,6BAA6B,2BAA2B,CAAC;;kCACxI,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,KAAK,YAAY,KAAK,uBACrB;;sDACE,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;;iEAIP;;sDACE,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAG,WAAU;sDACX,EAAE;;;;;;;;;;;;;0CAOX,8OAAC,sKAAA,CAAA,UAAoB;gCAAC,QAAQ,KAAK,GAAG;gCAAE,YAAY,KAAK,MAAM;;;;;;;;;;;;oBAIhE,KAAK,YAAY,KAAK,wBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;;;;;;;0CAGjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;oBAOpC,KAAK,YAAY,KAAK,wBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC,EAAE;;;;;;0CAClD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;oBAOpC,KAAK,kBAAkB,kBACtB,8OAAC;wBAAI,WAAW,CAAC,+BAA+B,EAAE,KAAK,YAAY,KAAK,SAAS,oBAAoB,mBAAmB;;0CACtH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,aAAa,EAAE,KAAK,YAAY,KAAK,SAAS,kBAAkB,iBAAiB;;;;;;0DACvG,8OAAC;gDAAK,WAAU;0DAAqC,EAAE;;;;;;;;;;;;kDAEzD,8OAAC;wCAAK,WAAW,CAAC,sBAAsB,EAAE,KAAK,YAAY,KAAK,SAAS,kBAAkB,iBAAiB;kDACzG,WAAW,KAAK,kBAAkB,EAAE;;;;;;;;;;;;4BAGxC;gCAAC;gCAAW;6BAAW,CAAC,QAAQ,CAAC,KAAK,MAAM,mBAC3C,8OAAC;gCAAE,WAAU;;oCACV,EAAE;oCACF,KAAK,YAAY,KAAK,UAAU,EAAE;;;;;;;;;;;;;;;;;;;0BAQ7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;kCAEL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAK,EAAE;;;;;;;;;;;;;;;;;0BAKZ,8OAAC,+JAAA,CAAA,UAAa;gBAAC,QAAQ;;;;;;;;;;;;AAG7B", "debugId": null}}]}