{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;AACH,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,4PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,uHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,SAAS,uHAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts"], "sourcesContent": ["// 国际化分类配置文件\n// 支持多语言的AI工具分类配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 分类的基础配置（不包含翻译文本）\nexport const CATEGORY_BASE_CONFIGS = [\n  {\n    slug: 'text-generation',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'data-analysis',\n    icon: '📊',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'audio-processing',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'education',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'marketing',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'productivity',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'customer-service',\n    icon: '🎧',\n    color: '#F59E0B'\n  }\n];\n\n// 客户端钩子：获取国际化的分类配置\nexport function useCategoryConfigs(): CategoryConfig[] {\n  const t = useTranslations('categories');\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 客户端钩子：获取分类选项（用于下拉框等）\nexport function useCategoryOptions(): CategoryOption[] {\n  const configs = useCategoryConfigs();\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 客户端钩子：获取包含\"所有分类\"选项的分类选项\nexport function useCategoryOptionsWithAll(): CategoryOption[] {\n  const t = useTranslations('categories');\n  const options = useCategoryOptions();\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 客户端钩子：获取分类名称\nexport function useCategoryName(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 客户端钩子：获取分类描述\nexport function useCategoryDescription(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取国际化的分类配置\nexport async function getCategoryConfigs(locale?: string): Promise<CategoryConfig[]> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 服务器端函数：获取分类选项\nexport async function getCategoryOptions(locale?: string): Promise<CategoryOption[]> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 服务器端函数：获取包含\"所有分类\"选项的分类选项\nexport async function getCategoryOptionsWithAll(locale?: string): Promise<CategoryOption[]> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  const options = await getCategoryOptions(locale);\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 服务器端函数：获取分类名称\nexport async function getCategoryName(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 服务器端函数：获取分类描述\nexport async function getCategoryDescription(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale, namespace: 'categories' });\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取分类配置\nexport async function getCategoryConfig(slug: string, locale?: string): Promise<CategoryConfig | undefined> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.find(config => config.slug === slug);\n}\n\n// 验证分类是否存在的辅助函数\nexport function isValidCategory(slug: string): boolean {\n  return CATEGORY_BASE_CONFIGS.some(config => config.slug === slug);\n}\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map(config => config.slug);\n\n// 分类元数据映射（slug -> 基础配置）\nexport const CATEGORY_BASE_METADATA: Record<string, typeof CATEGORY_BASE_CONFIGS[0]> = \n  CATEGORY_BASE_CONFIGS.reduce((acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  }, {} as Record<string, typeof CATEGORY_BASE_CONFIGS[0]>);\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;;;;;;AAEjB;AACA;;;AAgBO,MAAM,wBAAwB;IACnC;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,UAAU;IAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU;IAEhB,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,SAAS,gBAAgB,IAAY;IAC1C,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,SAAS,uBAAuB,IAAY;IACjD,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAElE,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,eAAe,0BAA0B,MAAe;IAC7D,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,MAAM,UAAU,MAAM,mBAAmB;IAEzC,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,eAAe,gBAAgB,IAAY,EAAE,MAAe;IACjE,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,eAAe,uBAAuB,IAAY,EAAE,MAAe;IACxE,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAa;IAClE,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,kBAAkB,IAAY,EAAE,MAAe;IACnE,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAGO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,MAAM,iBAAiB,sBAAsB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;AAGtE,MAAM,yBACX,sBAAsB,MAAM,CAAC,CAAC,KAAK;IACjC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { CATEGORY_SLUGS } from '@/constants/categories-i18n';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  launchDate?: Date; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    maxlength: [100, 'Tool name cannot exceed 100 characters']\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Tagline cannot exceed 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [2000, 'Long description cannot exceed 2000 characters']\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: CATEGORY_SLUGS\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Pricing details cannot exceed 500 characters']\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  launchDate: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ launchDate: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA2CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAuC;IAC1D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAiD;IACrE;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM,wIAAA,CAAA,iBAAc;IACtB;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAA+C;IAClE;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;SAAW;QAClD,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,YAAY,CAAC;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IOrder extends Document {\n  userId: string; // 用户ID\n  toolId: string; // 工具ID\n  type: 'launch_date_priority'; // 订单类型\n  amount: number; // 金额（分为单位）\n  currency: string; // 货币类型\n  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';\n  \n  // 支付相关\n  paymentMethod?: string; // 支付方式\n  paymentIntentId?: string; // Stripe payment intent ID (已弃用，使用stripePaymentIntentId)\n  paymentSessionId?: string; // Stripe checkout session ID\n  stripePaymentIntentId?: string; // Stripe payment intent ID\n  stripeCustomerId?: string; // Stripe customer ID\n  stripePaymentDetails?: {\n    paymentIntentId: string;\n    amount: number;\n    currency: string;\n    status: string;\n    created: Date;\n    failureReason?: string;\n  };\n  \n  // 订单详情\n  description: string; // 订单描述\n  selectedLaunchDate: Date; // 选择的发布日期\n  \n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  paidAt?: Date;\n  cancelledAt?: Date;\n  refundedAt?: Date;\n}\n\nconst OrderSchema: Schema = new Schema({\n  userId: {\n    type: Schema.Types.ObjectId,\n    ref: 'User',\n    required: [true, 'User ID is required']\n  },\n  toolId: {\n    type: Schema.Types.ObjectId,\n    ref: 'Tool',\n    required: [true, 'Tool ID is required']\n  },\n  type: {\n    type: String,\n    required: true,\n    enum: ['launch_date_priority'],\n    default: 'launch_date_priority'\n  },\n  amount: {\n    type: Number,\n    required: [true, 'Amount is required'],\n    min: [0, 'Amount must be positive']\n  },\n  currency: {\n    type: String,\n    required: true,\n    default: 'CNY',\n    enum: ['CNY', 'USD']\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],\n    default: 'pending'\n  },\n  \n  // 支付相关\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paymentIntentId: {\n    type: String,\n    trim: true\n  },\n  paymentSessionId: {\n    type: String,\n    trim: true\n  },\n  stripePaymentIntentId: {\n    type: String,\n    trim: true\n  },\n  stripeCustomerId: {\n    type: String,\n    trim: true\n  },\n  stripePaymentDetails: {\n    paymentIntentId: String,\n    amount: Number,\n    currency: String,\n    status: String,\n    created: Date,\n    failureReason: String\n  },\n  \n  // 订单详情\n  description: {\n    type: String,\n    required: [true, 'Description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  selectedLaunchDate: {\n    type: Date,\n    required: [true, 'Selected launch date is required']\n  },\n  \n  // 时间戳\n  paidAt: {\n    type: Date\n  },\n  cancelledAt: {\n    type: Date\n  },\n  refundedAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// 索引\nOrderSchema.index({ userId: 1, createdAt: -1 });\nOrderSchema.index({ toolId: 1 });\nOrderSchema.index({ status: 1 });\nOrderSchema.index({ paymentIntentId: 1 });\nOrderSchema.index({ paymentSessionId: 1 });\nOrderSchema.index({ stripePaymentIntentId: 1 });\nOrderSchema.index({ stripeCustomerId: 1 });\n\n// 虚拟字段\nOrderSchema.virtual('user', {\n  ref: 'User',\n  localField: 'userId',\n  foreignField: '_id',\n  justOne: true\n});\n\nOrderSchema.virtual('tool', {\n  ref: 'Tool',\n  localField: 'toolId',\n  foreignField: '_id',\n  justOne: true\n});\n\n// 实例方法\nOrderSchema.methods.markAsPaid = function() {\n  this.status = 'completed';\n  this.paidAt = new Date();\n  return this.save();\n};\n\nOrderSchema.methods.markAsFailed = function() {\n  this.status = 'failed';\n  return this.save();\n};\n\nOrderSchema.methods.cancel = function() {\n  this.status = 'cancelled';\n  this.cancelledAt = new Date();\n  return this.save();\n};\n\nOrderSchema.methods.refund = function() {\n  this.status = 'refunded';\n  this.refundedAt = new Date();\n  return this.save();\n};\n\nexport default mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,cAAsB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACrC,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,QAAQ;QACN,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;QAC3B,KAAK;QACL,UAAU;YAAC;YAAM;SAAsB;IACzC;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;SAAuB;QAC9B,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAAqB;QACtC,KAAK;YAAC;YAAG;SAA0B;IACrC;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,SAAS;QACT,MAAM;YAAC;YAAO;SAAM;IACtB;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAW;YAAa;YAAU;YAAa;SAAW;QACjE,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;QACN,MAAM;IACR;IACA,uBAAuB;QACrB,MAAM;QACN,MAAM;IACR;IACA,kBAAkB;QAChB,MAAM;QACN,MAAM;IACR;IACA,sBAAsB;QACpB,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS;QACT,eAAe;IACjB;IAEA,OAAO;IACP,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,oBAAoB;QAClB,MAAM;QACN,UAAU;YAAC;YAAM;SAAmC;IACtD;IAEA,MAAM;IACN,QAAQ;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,KAAK;AACL,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC9B,YAAY,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC9B,YAAY,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACvC,YAAY,KAAK,CAAC;IAAE,kBAAkB;AAAE;AACxC,YAAY,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,kBAAkB;AAAE;AAExC,OAAO;AACP,YAAY,OAAO,CAAC,QAAQ;IAC1B,KAAK;IACL,YAAY;IACZ,cAAc;IACd,SAAS;AACX;AAEA,YAAY,OAAO,CAAC,QAAQ;IAC1B,KAAK;IACL,YAAY;IACZ,cAAc;IACd,SAAS;AACX;AAEA,OAAO;AACP,YAAY,OAAO,CAAC,UAAU,GAAG;IAC/B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,MAAM,GAAG,IAAI;IAClB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,YAAY,GAAG;IACjC,IAAI,CAAC,MAAM,GAAG;IACd,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,MAAM,GAAG;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,WAAW,GAAG,IAAI;IACvB,OAAO,IAAI,CAAC,IAAI;AAClB;AAEA,YAAY,OAAO,CAAC,MAAM,GAAG;IAC3B,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG,IAAI;IACtB,OAAO,IAAI,CAAC,IAAI;AAClB;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqCA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport dbConnect from './mongodb';\nimport User from '../models/User';\nimport { getNextAuthUrl } from './env';\n\nexport const authOptions: NextAuthOptions = {\n  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略\n  // adapter: MongoDBAdapter(client),\n\n  // 动态配置基础URL，支持不同环境\n  // NextAuth会自动从环境变量或请求头中检测URL，但我们也可以显式设置\n  // 在生产环境中，这将被环境变量覆盖\n  ...(process.env.NODE_ENV === 'development' && {\n    // 仅在开发环境中设置，生产环境让NextAuth自动检测\n    // 这样可以支持不同的开发端口\n  }),\n\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      id: 'email-code',\n      name: 'Email Code',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        code: { label: 'Code', type: 'text' },\n        token: { label: 'Token', type: 'text' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.code || !credentials?.token) {\n          return null;\n        }\n\n        try {\n          await dbConnect();\n\n          // 查找用户\n          const user = await User.findOne({\n            email: credentials.email.toLowerCase(),\n            emailVerificationExpires: { $gt: new Date() }\n          });\n\n          if (!user) {\n            return null;\n          }\n\n          // 验证token和验证码\n          const storedData = user.emailVerificationToken;\n          if (!storedData || !storedData.includes(':')) {\n            return null;\n          }\n\n          const [storedToken, storedCode] = storedData.split(':');\n\n          if (storedToken !== credentials.token || storedCode !== credentials.code) {\n            return null;\n          }\n\n          // 验证成功，更新用户状态\n          user.emailVerified = true;\n          user.emailVerificationToken = undefined;\n          user.emailVerificationExpires = undefined;\n          user.lastLoginAt = new Date();\n\n          // 如果用户没有邮箱账户记录，添加一个\n          const hasEmailAccount = user.accounts.some((acc: any) => acc.provider === 'email');\n          if (!hasEmailAccount) {\n            user.accounts.push({\n              provider: 'email',\n              providerId: 'email',\n              providerAccountId: user.email,\n            });\n          }\n\n          await user.save();\n\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            name: user.name,\n            image: user.avatar,\n            role: user.role,\n          };\n        } catch (error) {\n          console.error('Email code authorization error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      // 对于credentials provider，用户已经在authorize中处理过了\n      if (account?.provider === 'email-code') {\n        return true;\n      }\n\n      await dbConnect();\n\n      try {\n        // 查找或创建用户（仅用于OAuth providers）\n        let existingUser = await User.findOne({ email: user.email });\n\n        if (!existingUser) {\n          // 创建新用户\n          existingUser = new User({\n            email: user.email,\n            name: user.name || profile?.name || 'User',\n            avatar: user.image || profile?.image,\n            emailVerified: true, // OAuth登录默认已验证\n            lastLoginAt: new Date(),\n          });\n          await existingUser.save();\n        } else {\n          // 更新最后登录时间\n          existingUser.lastLoginAt = new Date();\n          await existingUser.save();\n        }\n\n        // 添加或更新账户信息\n        if (account && account.provider !== 'email-code') {\n          existingUser.addAccount({\n            provider: account.provider as 'google' | 'github',\n            providerId: account.provider,\n            providerAccountId: account.providerAccountId || account.id || '',\n            accessToken: account.access_token,\n            refreshToken: account.refresh_token,\n            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,\n          });\n          await existingUser.save();\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Sign in error:', error);\n        return false;\n      }\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        // 对于credentials provider，user对象已经包含了我们需要的信息\n        token.userId = user.id;\n        token.role = (user as any).role || 'user';\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        (session.user as any).id = token.userId as string;\n        (session.user as any).role = token.role as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,cAA+B;IAC1C,iDAAiD;IACjD,mCAAmC;IAEnC,mBAAmB;IACnB,wCAAwC;IACxC,mBAAmB;IACnB,GAAI,oDAAyB,iBAAiB;IAG9C,CAAC;IAED,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,MAAM;oBAAE,OAAO;oBAAQ,MAAM;gBAAO;gBACpC,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;YACxC;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;oBACpE,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;oBAEd,OAAO;oBACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,YAAY,KAAK,CAAC,WAAW;wBACpC,0BAA0B;4BAAE,KAAK,IAAI;wBAAO;oBAC9C;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,cAAc;oBACd,MAAM,aAAa,KAAK,sBAAsB;oBAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM;wBAC5C,OAAO;oBACT;oBAEA,MAAM,CAAC,aAAa,WAAW,GAAG,WAAW,KAAK,CAAC;oBAEnD,IAAI,gBAAgB,YAAY,KAAK,IAAI,eAAe,YAAY,IAAI,EAAE;wBACxE,OAAO;oBACT;oBAEA,cAAc;oBACd,KAAK,aAAa,GAAG;oBACrB,KAAK,sBAAsB,GAAG;oBAC9B,KAAK,wBAAwB,GAAG;oBAChC,KAAK,WAAW,GAAG,IAAI;oBAEvB,oBAAoB;oBACpB,MAAM,kBAAkB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK;oBAC1E,IAAI,CAAC,iBAAiB;wBACpB,KAAK,QAAQ,CAAC,IAAI,CAAC;4BACjB,UAAU;4BACV,YAAY;4BACZ,mBAAmB,KAAK,KAAK;wBAC/B;oBACF;oBAEA,MAAM,KAAK,IAAI;oBAEf,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,MAAM;wBAClB,MAAM,KAAK,IAAI;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,6CAA6C;YAC7C,IAAI,SAAS,aAAa,cAAc;gBACtC,OAAO;YACT;YAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YAEd,IAAI;gBACF,8BAA8B;gBAC9B,IAAI,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,KAAK,KAAK;gBAAC;gBAE1D,IAAI,CAAC,cAAc;oBACjB,QAAQ;oBACR,eAAe,IAAI,uHAAA,CAAA,UAAI,CAAC;wBACtB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;wBACpC,QAAQ,KAAK,KAAK,IAAI,SAAS;wBAC/B,eAAe;wBACf,aAAa,IAAI;oBACnB;oBACA,MAAM,aAAa,IAAI;gBACzB,OAAO;oBACL,WAAW;oBACX,aAAa,WAAW,GAAG,IAAI;oBAC/B,MAAM,aAAa,IAAI;gBACzB;gBAEA,YAAY;gBACZ,IAAI,WAAW,QAAQ,QAAQ,KAAK,cAAc;oBAChD,aAAa,UAAU,CAAC;wBACtB,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,QAAQ;wBAC5B,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,EAAE,IAAI;wBAC9D,aAAa,QAAQ,YAAY;wBACjC,cAAc,QAAQ,aAAa;wBACnC,WAAW,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,QAAQ;oBACxE;oBACA,MAAM,aAAa,IAAI;gBACzB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;YACT;QACF;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,KAAK,EAAE;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,IAAI;YACrC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,MAAM;gBACtC,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 1395, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts"], "sourcesContent": ["// API 响应消息国际化\nexport interface ApiMessages {\n  // 通用错误消息\n  errors: {\n    fetch_failed: string;\n    network_error: string;\n    validation_failed: string;\n    unauthorized: string;\n    forbidden: string;\n    not_found: string;\n    internal_error: string;\n    invalid_request: string;\n    missing_required_field: string;\n    duplicate_name: string;\n    create_failed: string;\n    update_failed: string;\n    delete_failed: string;\n  };\n  \n  // 成功消息\n  success: {\n    created: string;\n    updated: string;\n    deleted: string;\n    submitted: string;\n    approved: string;\n    rejected: string;\n    published: string;\n  };\n  \n  // 工具相关消息\n  tools: {\n    fetch_failed: string;\n    create_failed: string;\n    name_required: string;\n    description_required: string;\n    website_required: string;\n    category_required: string;\n    pricing_required: string;\n    submitter_name_required: string;\n    submitter_email_required: string;\n    name_exists: string;\n    submit_success: string;\n    approve_success: string;\n    reject_success: string;\n    approve_failed: string;\n    reject_failed: string;\n    not_found: string;\n    update_success: string;\n    update_failed: string;\n    launch_date_already_set: string;\n    free_date_restriction: string;\n    paid_date_restriction: string;\n    launch_date_set_success: string;\n    edit_not_allowed: string;\n    already_published: string;\n    launch_date_updated: string;\n  };\n  \n  // 用户相关消息\n  user: {\n    not_found: string;\n    unauthorized: string;\n    profile_update_success: string;\n    profile_update_failed: string;\n  };\n  \n  // 认证相关消息\n  auth: {\n    invalid_credentials: string;\n    code_sent: string;\n    code_send_failed: string;\n    invalid_code: string;\n    login_success: string;\n    login_failed: string;\n    logout_success: string;\n  };\n  \n  // 支付相关消息\n  payment: {\n    create_intent_failed: string;\n    payment_success: string;\n    payment_failed: string;\n    webhook_error: string;\n    order_created: string;\n    upgrade_order_created: string;\n  };\n\n  // 上传相关消息\n  upload: {\n    no_file: string;\n    invalid_type: string;\n    file_too_large: string;\n    upload_failed: string;\n    upload_success: string;\n  };\n}\n\n// 中文消息\nexport const zhMessages: ApiMessages = {\n  errors: {\n    fetch_failed: '获取数据失败',\n    network_error: '网络错误，请重试',\n    validation_failed: '验证失败',\n    unauthorized: '未授权访问',\n    forbidden: '禁止访问',\n    not_found: '资源未找到',\n    internal_error: '服务器内部错误',\n    invalid_request: '无效请求',\n    missing_required_field: '缺少必需字段',\n    duplicate_name: '名称已存在',\n    create_failed: '创建失败',\n    update_failed: '更新失败',\n    delete_failed: '删除失败',\n  },\n  success: {\n    created: '创建成功',\n    updated: '更新成功',\n    deleted: '删除成功',\n    submitted: '提交成功',\n    approved: '批准成功',\n    rejected: '拒绝成功',\n    published: '发布成功',\n  },\n  tools: {\n    fetch_failed: '获取工具列表失败',\n    create_failed: '创建工具失败',\n    name_required: 'name 是必需的',\n    description_required: 'description 是必需的',\n    website_required: 'website 是必需的',\n    category_required: 'category 是必需的',\n    pricing_required: 'pricing 是必需的',\n    submitter_name_required: 'submitterName 是必需的',\n    submitter_email_required: 'submitterEmail 是必需的',\n    name_exists: '该工具名称已存在',\n    submit_success: '工具提交成功，等待审核',\n    approve_success: '工具审核通过',\n    reject_success: '工具已拒绝',\n    approve_failed: '审核通过失败',\n    reject_failed: '拒绝失败',\n    not_found: '工具未找到',\n    update_success: '工具更新成功',\n    update_failed: '工具更新失败',\n    launch_date_already_set: '此工具已经选择了发布日期',\n    free_date_restriction: '免费选项只能选择一个月后的日期',\n    paid_date_restriction: '付费选项最早只能选择明天的日期',\n    launch_date_set_success: '发布日期设置成功，工具已进入审核队列',\n    edit_not_allowed: '当前状态不允许修改发布日期',\n    already_published: '工具已发布，无法修改发布日期',\n    launch_date_updated: '发布日期修改成功',\n    publish_success: '工具发布成功',\n    publish_failed: '工具发布失败',\n  },\n  user: {\n    not_found: '用户未找到',\n    unauthorized: '用户未授权',\n    profile_update_success: '个人资料更新成功',\n    profile_update_failed: '个人资料更新失败',\n  },\n  auth: {\n    invalid_credentials: '无效的登录凭据',\n    code_sent: '验证码已发送',\n    code_send_failed: '验证码发送失败',\n    invalid_code: '无效的验证码',\n    login_success: '登录成功',\n    login_failed: '登录失败',\n    logout_success: '退出成功',\n  },\n  payment: {\n    create_intent_failed: '创建支付意图失败',\n    payment_success: '支付成功',\n    payment_failed: '支付失败',\n    webhook_error: 'Webhook 处理错误',\n    order_created: '订单创建成功，请完成支付',\n    upgrade_order_created: '升级订单创建成功，请完成支付',\n  },\n  upload: {\n    no_file: '请选择要上传的文件',\n    invalid_type: '只支持 JPEG、PNG、GIF、WebP 格式的图片',\n    file_too_large: '文件大小不能超过 5MB',\n    upload_failed: '文件上传失败',\n    upload_success: '文件上传成功',\n  },\n};\n\n// 英文消息\nexport const enMessages: ApiMessages = {\n  errors: {\n    fetch_failed: 'Failed to fetch data',\n    network_error: 'Network error, please try again',\n    validation_failed: 'Validation failed',\n    unauthorized: 'Unauthorized access',\n    forbidden: 'Access forbidden',\n    not_found: 'Resource not found',\n    internal_error: 'Internal server error',\n    invalid_request: 'Invalid request',\n    missing_required_field: 'Missing required field',\n    duplicate_name: 'Name already exists',\n    create_failed: 'Creation failed',\n    update_failed: 'Update failed',\n    delete_failed: 'Deletion failed',\n  },\n  success: {\n    created: 'Created successfully',\n    updated: 'Updated successfully',\n    deleted: 'Deleted successfully',\n    submitted: 'Submitted successfully',\n    approved: 'Approved successfully',\n    rejected: 'Rejected successfully',\n    published: 'Published successfully',\n  },\n  tools: {\n    fetch_failed: 'Failed to fetch tools list',\n    create_failed: 'Failed to create tool',\n    name_required: 'name is required',\n    description_required: 'description is required',\n    website_required: 'website is required',\n    category_required: 'category is required',\n    pricing_required: 'pricing is required',\n    submitter_name_required: 'submitterName is required',\n    submitter_email_required: 'submitterEmail is required',\n    name_exists: 'Tool name already exists',\n    submit_success: 'Tool submitted successfully, awaiting review',\n    approve_success: 'Tool approved successfully',\n    reject_success: 'Tool rejected successfully',\n    approve_failed: 'Failed to approve tool',\n    reject_failed: 'Failed to reject tool',\n    not_found: 'Tool not found',\n    update_success: 'Tool updated successfully',\n    update_failed: 'Failed to update tool',\n    launch_date_already_set: 'This tool has already selected a launch date',\n    free_date_restriction: 'Free option can only select dates one month later',\n    paid_date_restriction: 'Paid option can only select dates from tomorrow',\n    launch_date_set_success: 'Launch date set successfully, tool entered review queue',\n    edit_not_allowed: 'Current status does not allow modifying launch date',\n    already_published: 'Tool already published, cannot modify launch date',\n    launch_date_updated: 'Launch date updated successfully',\n    publish_success: 'Tool published successfully',\n    publish_failed: 'Failed to publish tool',\n  },\n  user: {\n    not_found: 'User not found',\n    unauthorized: 'User unauthorized',\n    profile_update_success: 'Profile updated successfully',\n    profile_update_failed: 'Failed to update profile',\n  },\n  auth: {\n    invalid_credentials: 'Invalid credentials',\n    code_sent: 'Verification code sent',\n    code_send_failed: 'Failed to send verification code',\n    invalid_code: 'Invalid verification code',\n    login_success: 'Login successful',\n    login_failed: 'Login failed',\n    logout_success: 'Logout successful',\n  },\n  payment: {\n    create_intent_failed: 'Failed to create payment intent',\n    payment_success: 'Payment successful',\n    payment_failed: 'Payment failed',\n    webhook_error: 'Webhook processing error',\n    order_created: 'Order created successfully, please complete payment',\n    upgrade_order_created: 'Upgrade order created successfully, please complete payment',\n  },\n  upload: {\n    no_file: 'Please select a file to upload',\n    invalid_type: 'Only JPEG, PNG, GIF, WebP image formats are supported',\n    file_too_large: 'File size cannot exceed 5MB',\n    upload_failed: 'File upload failed',\n    upload_success: 'File uploaded successfully',\n  },\n};\n\n// 获取API消息的工具函数\nexport function getApiMessage(locale: 'zh' | 'en', key: string): string {\n  const messages = locale === 'zh' ? zhMessages : enMessages;\n  \n  // 支持嵌套键，如 'tools.fetch_failed'\n  const keys = key.split('.');\n  let message: any = messages;\n  \n  for (const k of keys) {\n    if (message && typeof message === 'object' && k in message) {\n      message = message[k];\n    } else {\n      // 如果找不到对应的键，返回默认的中文消息\n      return locale === 'zh' ? '操作失败' : 'Operation failed';\n    }\n  }\n  \n  return typeof message === 'string' ? message : (locale === 'zh' ? '操作失败' : 'Operation failed');\n}\n\n// 从请求头中获取语言偏好\nexport function getLocaleFromRequest(request: Request): 'zh' | 'en' {\n  // 首先检查自定义的 X-Locale 头\n  const xLocale = request.headers.get('x-locale');\n  if (xLocale === 'en' || xLocale === 'zh') {\n    return xLocale;\n  }\n\n  const acceptLanguage = request.headers.get('accept-language') || '';\n  const pathname = new URL(request.url).pathname;\n\n  // 然后检查URL路径中的语言前缀\n  if (pathname.startsWith('/en/')) {\n    return 'en';\n  } else if (pathname.startsWith('/zh/')) {\n    return 'zh';\n  }\n\n  // 最后检查Accept-Language头\n  if (acceptLanguage.includes('en')) {\n    return 'en';\n  }\n\n  // 默认返回中文\n  return 'zh';\n}\n"], "names": [], "mappings": "AAAA,cAAc;;;;;;;AAmGP,MAAM,aAA0B;IACrC,QAAQ;QACN,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,wBAAwB;QACxB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,SAAS;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;QACzB,0BAA0B;QAC1B,aAAa;QACb,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,yBAAyB;QACzB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM;QACJ,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,uBAAuB;IACzB;IACA,MAAM;QACJ,qBAAqB;QACrB,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;IACA,SAAS;QACP,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,uBAAuB;IACzB;IACA,QAAQ;QACN,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAGO,MAAM,aAA0B;IACrC,QAAQ;QACN,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,wBAAwB;QACxB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,SAAS;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;QACzB,0BAA0B;QAC1B,aAAa;QACb,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,yBAAyB;QACzB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM;QACJ,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,uBAAuB;IACzB;IACA,MAAM;QACJ,qBAAqB;QACrB,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;IACA,SAAS;QACP,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,uBAAuB;IACzB;IACA,QAAQ;QACN,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAGO,SAAS,cAAc,MAAmB,EAAE,GAAW;IAC5D,MAAM,WAAW,WAAW,OAAO,aAAa;IAEhD,+BAA+B;IAC/B,MAAM,OAAO,IAAI,KAAK,CAAC;IACvB,IAAI,UAAe;IAEnB,KAAK,MAAM,KAAK,KAAM;QACpB,IAAI,WAAW,OAAO,YAAY,YAAY,KAAK,SAAS;YAC1D,UAAU,OAAO,CAAC,EAAE;QACtB,OAAO;YACL,sBAAsB;YACtB,OAAO,WAAW,OAAO,SAAS;QACpC;IACF;IAEA,OAAO,OAAO,YAAY,WAAW,UAAW,WAAW,OAAO,SAAS;AAC7E;AAGO,SAAS,qBAAqB,OAAgB;IACnD,sBAAsB;IACtB,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;IACpC,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,OAAO;IACT;IAEA,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACjE,MAAM,WAAW,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAQ;IAE9C,kBAAkB;IAClB,IAAI,SAAS,UAAU,CAAC,SAAS;QAC/B,OAAO;IACT,OAAO,IAAI,SAAS,UAAU,CAAC,SAAS;QACtC,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,eAAe,QAAQ,CAAC,OAAO;QACjC,OAAO;IACT;IAEA,SAAS;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1614, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/%5Bid%5D/launch-date/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\nimport Order from '@/models/Order';\nimport User from '@/models/User';\nimport { authOptions } from '@/lib/auth';\nimport { PRICING_CONFIG } from '@/constants/pricing';\nimport mongoose from 'mongoose';\nimport { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';\n\n// POST /api/tools/[id]/launch-date - 设置工具发布日期和选项\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // 检查用户认证\n    const session = await getServerSession(authOptions);\n    const locale = getLocaleFromRequest(request);\n\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.unauthorized') },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { id } = await params;\n    const { launchOption, selectedDate } = await request.json();\n\n    // 验证ID格式\n    if (!mongoose.Types.ObjectId.isValid(id)) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },\n        { status: 400 }\n      );\n    }\n\n    // 验证输入\n    if (!launchOption || !selectedDate) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },\n        { status: 400 }\n      );\n    }\n\n    if (!['free', 'paid'].includes(launchOption)) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },\n        { status: 400 }\n      );\n    }\n\n    // 获取用户信息\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.not_found') },\n        { status: 404 }\n      );\n    }\n\n    // 查找工具\n    const tool = await Tool.findById(id);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.not_found') },\n        { status: 404 }\n      );\n    }\n\n    // 检查工具所有权\n    if (tool.submittedBy !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.forbidden') },\n        { status: 403 }\n      );\n    }\n\n    // 检查工具状态 - 允许draft状态或者免费用户升级到付费\n    if (tool.status !== 'draft' && !(tool.status === 'pending' && tool.launchOption === 'free' && launchOption === 'paid')) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.launch_date_already_set') },\n        { status: 400 }\n      );\n    }\n\n    const selectedLaunchDate = new Date(selectedDate);\n    const now = new Date();\n\n    // 验证日期\n    if (launchOption === 'free') {\n      // 免费选项：必须是一个月后或更晚的日期\n      const oneMonthLater = new Date();\n      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);\n      oneMonthLater.setHours(0, 0, 0, 0);\n\n      const selectedDateOnly = new Date(selectedLaunchDate);\n      selectedDateOnly.setHours(0, 0, 0, 0);\n\n      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {\n        return NextResponse.json(\n          { success: false, message: getApiMessage(locale, 'tools.free_date_restriction') },\n          { status: 400 }\n        );\n      }\n    } else {\n      // 付费选项：必须是明天或以后的日期\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      tomorrow.setHours(0, 0, 0, 0);\n\n      if (selectedLaunchDate < tomorrow) {\n        return NextResponse.json(\n          { success: false, message: getApiMessage(locale, 'tools.paid_date_restriction') },\n          { status: 400 }\n        );\n      }\n    }\n\n    if (launchOption === 'free') {\n      // 免费选项：直接更新工具状态并进入审核\n      await Tool.findByIdAndUpdate(id, {\n        $set: {\n          launchDateSelected: true,\n          selectedLaunchDate,\n          launchDate: selectedLaunchDate, // 同时设置launchDate\n          launchOption: 'free',\n          paymentRequired: false,\n          status: 'pending' // 进入审核队列\n        }\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          message: getApiMessage(locale, 'tools.launch_date_set_success')\n        }\n      });\n\n    } else {\n      // 付费选项：创建订单\n      const paymentAmount = PRICING_CONFIG.PRIORITY_LAUNCH.stripeAmount;\n\n      const order = new Order({\n        userId: user._id,\n        toolId: id,\n        type: 'launch_date_priority',\n        amount: paymentAmount,\n        currency: PRICING_CONFIG.PRIORITY_LAUNCH.currency,\n        status: 'pending',\n        description: `工具 \"${tool.name}\" 优先发布服务`,\n        selectedLaunchDate\n      });\n\n      await order.save();\n\n      // 更新工具状态\n      const updateData: any = {\n        launchDateSelected: true,\n        selectedLaunchDate,\n        launchDate: selectedLaunchDate, // 同时设置launchDate\n        launchOption: 'paid',\n        paymentRequired: true,\n        paymentAmount,\n        paymentStatus: 'pending',\n        orderId: order._id.toString()\n      };\n\n      // 付费工具在付费成功前始终保持draft状态\n      updateData.status = 'draft';\n\n      await Tool.findByIdAndUpdate(id, { $set: updateData });\n\n      // 这里应该集成真实的支付系统（如Stripe、支付宝等）\n      // 现在返回一个模拟的支付URL，包含locale前缀\n      const paymentUrl = `/${locale}/payment/checkout?orderId=${order._id}`;\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          orderId: order._id,\n          paymentUrl,\n          amount: paymentAmount,\n          message: tool.status === 'pending' ? getApiMessage(locale, 'payment.upgrade_order_created') : getApiMessage(locale, 'payment.order_created')\n        }\n      });\n    }\n\n  } catch (error) {\n    console.error('Launch date selection error:', error);\n    const locale = getLocaleFromRequest(request);\n    return NextResponse.json(\n      { success: false, message: getApiMessage(locale, 'errors.internal_error') },\n      { status: 500 }\n    );\n  }\n}\n\n// GET /api/tools/[id]/launch-date - 获取工具发布日期信息\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n    const locale = getLocaleFromRequest(request);\n\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.unauthorized') },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { id } = await params;\n\n    if (!mongoose.Types.ObjectId.isValid(id)) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },\n        { status: 400 }\n      );\n    }\n\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.not_found') },\n        { status: 404 }\n      );\n    }\n\n    const tool = await Tool.findById(id);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.not_found') },\n        { status: 404 }\n      );\n    }\n\n    // 检查工具所有权\n    if (tool.submittedBy !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.forbidden') },\n        { status: 403 }\n      );\n    }\n\n    // 如果有订单，获取订单信息\n    let order = null;\n    if (tool.orderId) {\n      order = await Order.findById(tool.orderId);\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        tool: {\n          id: tool._id,\n          name: tool.name,\n          status: tool.status,\n          launchDateSelected: tool.launchDateSelected,\n          selectedLaunchDate: tool.selectedLaunchDate,\n          launchOption: tool.launchOption,\n          paymentRequired: tool.paymentRequired,\n          paymentStatus: tool.paymentStatus\n        },\n        order\n      }\n    });\n\n  } catch (error) {\n    console.error('Get launch date info error:', error);\n    const locale = getLocaleFromRequest(request);\n    return NextResponse.json(\n      { success: false, message: getApiMessage(locale, 'errors.internal_error') },\n      { status: 500 }\n    );\n  }\n}\n\n// PATCH /api/tools/[id]/launch-date - 修改工具发布日期（仅限付费用户）\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n    const locale = getLocaleFromRequest(request);\n\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.unauthorized') },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    const { id } = await params;\n    const { selectedDate } = await request.json();\n\n    if (!mongoose.Types.ObjectId.isValid(id)) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },\n        { status: 400 }\n      );\n    }\n\n    if (!selectedDate) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },\n        { status: 400 }\n      );\n    }\n\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.not_found') },\n        { status: 404 }\n      );\n    }\n\n    const tool = await Tool.findById(id);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.not_found') },\n        { status: 404 }\n      );\n    }\n\n    // 检查工具所有权\n    if (tool.submittedBy !== user._id.toString()) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'errors.forbidden') },\n        { status: 403 }\n      );\n    }\n\n    // 检查工具状态（只有还未发布的工具可以修改）\n    if (!['pending', 'approved'].includes(tool.status)) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.edit_not_allowed') },\n        { status: 400 }\n      );\n    }\n\n    // 检查是否已经发布\n    const now = new Date();\n    const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= now;\n    if (isPublished) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.already_published') },\n        { status: 400 }\n      );\n    }\n\n    const selectedLaunchDate = new Date(selectedDate);\n\n    // 根据付费状态验证日期\n    if (tool.launchOption === 'paid') {\n      // 付费用户：可以选择明天及以后的任意日期\n      const tomorrow = new Date();\n      tomorrow.setDate(tomorrow.getDate() + 1);\n      tomorrow.setHours(0, 0, 0, 0);\n\n      if (selectedLaunchDate < tomorrow) {\n        return NextResponse.json(\n          { success: false, message: getApiMessage(locale, 'tools.paid_date_restriction') },\n          { status: 400 }\n        );\n      }\n    } else {\n      // 免费用户：只能选择一个月后的日期\n      const oneMonthLater = new Date();\n      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);\n      oneMonthLater.setHours(0, 0, 0, 0);\n\n      const selectedDateOnly = new Date(selectedLaunchDate);\n      selectedDateOnly.setHours(0, 0, 0, 0);\n\n      if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {\n        return NextResponse.json(\n          { success: false, message: getApiMessage(locale, 'tools.free_date_restriction') },\n          { status: 400 }\n        );\n      }\n    }\n\n    // 更新工具的发布日期\n    await Tool.findByIdAndUpdate(id, {\n      $set: {\n        selectedLaunchDate,\n        launchDate: selectedLaunchDate // 同时更新launchDate\n      }\n    });\n\n    // 如果有关联的订单，也更新订单的发布日期\n    if (tool.orderId) {\n      await Order.findByIdAndUpdate(tool.orderId, {\n        $set: {\n          selectedLaunchDate\n        }\n      });\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        message: getApiMessage(locale, 'tools.launch_date_updated'),\n        selectedLaunchDate\n      }\n    });\n\n  } catch (error) {\n    console.error('Update launch date error:', error);\n    const locale = getLocaleFromRequest(request);\n    return NextResponse.json(\n      { success: false, message: getApiMessage(locale, 'errors.internal_error') },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAGO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,SAAS;QACT,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QAEpC,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAqB,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEzD,SAAS;QACT,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC;YAAC;YAAQ;SAAO,CAAC,QAAQ,CAAC,eAAe;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAkB,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAmB,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAoB,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,IAAI,KAAK,MAAM,KAAK,WAAW,CAAC,CAAC,KAAK,MAAM,KAAK,aAAa,KAAK,YAAY,KAAK,UAAU,iBAAiB,MAAM,GAAG;YACtH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAiC,GAClF;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,qBAAqB,IAAI,KAAK;QACpC,MAAM,MAAM,IAAI;QAEhB,OAAO;QACP,IAAI,iBAAiB,QAAQ;YAC3B,qBAAqB;YACrB,MAAM,gBAAgB,IAAI;YAC1B,cAAc,QAAQ,CAAC,cAAc,QAAQ,KAAK;YAClD,cAAc,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEhC,MAAM,mBAAmB,IAAI,KAAK;YAClC,iBAAiB,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEnC,IAAI,iBAAiB,OAAO,KAAK,cAAc,OAAO,IAAI;gBACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAA+B,GAChF;oBAAE,QAAQ;gBAAI;YAElB;QACF,OAAO;YACL,mBAAmB;YACnB,MAAM,WAAW,IAAI;YACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;YAE3B,IAAI,qBAAqB,UAAU;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAA+B,GAChF;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,IAAI,iBAAiB,QAAQ;YAC3B,qBAAqB;YACrB,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;gBAC/B,MAAM;oBACJ,oBAAoB;oBACpB;oBACA,YAAY;oBACZ,cAAc;oBACd,iBAAiB;oBACjB,QAAQ,UAAU,SAAS;gBAC7B;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACjC;YACF;QAEF,OAAO;YACL,YAAY;YACZ,MAAM,gBAAgB,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY;YAEjE,MAAM,QAAQ,IAAI,wHAAA,CAAA,UAAK,CAAC;gBACtB,QAAQ,KAAK,GAAG;gBAChB,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,UAAU,6HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,QAAQ;gBACjD,QAAQ;gBACR,aAAa,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ,CAAC;gBACvC;YACF;YAEA,MAAM,MAAM,IAAI;YAEhB,SAAS;YACT,MAAM,aAAkB;gBACtB,oBAAoB;gBACpB;gBACA,YAAY;gBACZ,cAAc;gBACd,iBAAiB;gBACjB;gBACA,eAAe;gBACf,SAAS,MAAM,GAAG,CAAC,QAAQ;YAC7B;YAEA,wBAAwB;YACxB,WAAW,MAAM,GAAG;YAEpB,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;gBAAE,MAAM;YAAW;YAEpD,8BAA8B;YAC9B,4BAA4B;YAC5B,MAAM,aAAa,CAAC,CAAC,EAAE,OAAO,0BAA0B,EAAE,MAAM,GAAG,EAAE;YAErE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,SAAS,MAAM,GAAG;oBAClB;oBACA,QAAQ;oBACR,SAAS,KAAK,MAAM,KAAK,YAAY,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,mCAAmC,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACtH;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAyB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QAEpC,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAqB,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAkB,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAmB,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAoB,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,IAAI,QAAQ;QACZ,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,KAAK,OAAO;QAC3C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,MAAM;oBACJ,IAAI,KAAK,GAAG;oBACZ,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;oBACnB,oBAAoB,KAAK,kBAAkB;oBAC3C,oBAAoB,KAAK,kBAAkB;oBAC3C,cAAc,KAAK,YAAY;oBAC/B,iBAAiB,KAAK,eAAe;oBACrC,eAAe,KAAK,aAAa;gBACnC;gBACA;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAyB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QAEpC,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAqB,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3C,IAAI,CAAC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAkB,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAmB,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,UAAU;QACV,IAAI,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,QAAQ,IAAI;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAoB,GACrE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,IAAI,CAAC;YAAC;YAAW;SAAW,CAAC,QAAQ,CAAC,KAAK,MAAM,GAAG;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA0B,GAC3E;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,KAAK,MAAM,KAAK,cAAc,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK;QAClG,IAAI,aAAa;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAA2B,GAC5E;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,qBAAqB,IAAI,KAAK;QAEpC,aAAa;QACb,IAAI,KAAK,YAAY,KAAK,QAAQ;YAChC,sBAAsB;YACtB,MAAM,WAAW,IAAI;YACrB,SAAS,OAAO,CAAC,SAAS,OAAO,KAAK;YACtC,SAAS,QAAQ,CAAC,GAAG,GAAG,GAAG;YAE3B,IAAI,qBAAqB,UAAU;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAA+B,GAChF;oBAAE,QAAQ;gBAAI;YAElB;QACF,OAAO;YACL,mBAAmB;YACnB,MAAM,gBAAgB,IAAI;YAC1B,cAAc,QAAQ,CAAC,cAAc,QAAQ,KAAK;YAClD,cAAc,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEhC,MAAM,mBAAmB,IAAI,KAAK;YAClC,iBAAiB,QAAQ,CAAC,GAAG,GAAG,GAAG;YAEnC,IAAI,iBAAiB,OAAO,KAAK,cAAc,OAAO,IAAI;gBACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAA+B,GAChF;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,YAAY;QACZ,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;YAC/B,MAAM;gBACJ;gBACA,YAAY,mBAAmB,iBAAiB;YAClD;QACF;QAEA,sBAAsB;QACtB,IAAI,KAAK,OAAO,EAAE;YAChB,MAAM,wHAAA,CAAA,UAAK,CAAC,iBAAiB,CAAC,KAAK,OAAO,EAAE;gBAC1C,MAAM;oBACJ;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAC/B;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAyB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}